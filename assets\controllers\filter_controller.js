import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ["form"];
    
    connect() {
        this.timeout = null;
    }
    
    filter() {
        this._performFilter();
    }
    
    debounce(event) {
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
            this._performFilter();
        }, 300);
    }
    
    _performFilter() {
        const form = document.getElementById('filter-form');
        const formData = new FormData(form);
        const params = new URLSearchParams();
        
        for (const [key, value] of formData.entries()) {
            if (value) {
                params.append(key, value);
            }
        }
        
        const url = `${form.action}?${params.toString()}`;
        
        fetch(url, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            document.getElementById('demandes-list').innerHTML = html;
            
            // Update URL without reloading the page
            history.pushState({}, '', url);
        })
        .catch(error => console.error('Error:', error));
    }
}
