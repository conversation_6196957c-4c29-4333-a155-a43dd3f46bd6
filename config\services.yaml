# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration

parameters:
    app.mailer_sender_email: '%env(MAILER_SENDER_EMAIL)%'
    app.mailer_sender_name: '%env(MAILER_SENDER_NAME)%'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    App\Service\LdapService:
        arguments:
            $host: '%env(LDAP_HOST)%'
            $username: '%env(LDAP_USERNAME)%'
            $password: '%env(LDAP_PASSWORD)%'

    App\Service\EmailService:
        arguments:
            $userRepository: '@App\Repository\UserRepository'
            $twig: '@twig'
            $senderEmail: '%app.mailer_sender_email%'
            $senderName: '%app.mailer_sender_name%'


