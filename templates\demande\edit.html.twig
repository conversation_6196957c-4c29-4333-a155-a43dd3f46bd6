{% extends 'base.html.twig' %}

{% block title %}Modifier la demande{% endblock %}

{% block body %}
<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold text-gray-900">Modifier la demande</h1>
        <div class="flex space-x-2">
            <a href="{{ path('app_demande_index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Retour à la liste
            </a>
            <a href="{{ path('app_demande_show', {'id': demande.id}) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Voir la demande
            </a>
        </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            {{ include('demande/_form.html.twig', {'button_label': 'Mettre à jour'}) }}
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Collection handling for items
            const collectionHolder = document.querySelector('[data-prototype]');
            const itemsContainer = document.getElementById('items-container');
            const addItemButton = document.getElementById('add-item');

            // Count the initial items
            let index = document.querySelectorAll('.item-row').length;

            // Add a new item
            addItemButton.addEventListener('click', function(e) {
                e.preventDefault();
                addItemForm();
            });

            function addItemForm() {
                // Get the prototype
                const prototype = collectionHolder.dataset.prototype;

                // Replace __name__ with the current index
                const newForm = prototype.replace(/__name__/g, index);

                // Create a temporary div to parse the form
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = newForm;

                // Create the main item div
                const itemDiv = document.createElement('div');
                itemDiv.classList.add('item-row', 'flex', 'items-start', 'space-x-2', 'mb-3', 'p-3', 'border', 'border-gray-200', 'rounded-md');

                // Create reference div
                const referenceDiv = document.createElement('div');
                referenceDiv.classList.add('flex-1');

                // Find reference label, input and errors
                const referenceLabel = tempDiv.querySelector('label[for$="_reference"]');
                const referenceInput = tempDiv.querySelector('input[id$="_reference"]');
                const referenceErrors = tempDiv.querySelector('div:has(input[id$="_reference"]) + div.invalid-feedback');

                if (referenceLabel) referenceDiv.appendChild(referenceLabel);
                if (referenceInput) referenceDiv.appendChild(referenceInput);
                if (referenceErrors) referenceDiv.appendChild(referenceErrors);

                // Create quantity div
                const quantiteDiv = document.createElement('div');
                quantiteDiv.classList.add('w-24');

                // Find quantity label, input and errors
                const quantiteLabel = tempDiv.querySelector('label[for$="_quantite"]');
                const quantiteInput = tempDiv.querySelector('input[id$="_quantite"]');
                const quantiteErrors = tempDiv.querySelector('div:has(input[id$="_quantite"]) + div.invalid-feedback');

                if (quantiteLabel) quantiteDiv.appendChild(quantiteLabel);
                if (quantiteInput) quantiteDiv.appendChild(quantiteInput);
                if (quantiteErrors) quantiteDiv.appendChild(quantiteErrors);

                // Add the divs to the item row
                itemDiv.appendChild(referenceDiv);
                itemDiv.appendChild(quantiteDiv);

                // Add delete button
                const deleteButton = document.createElement('button');
                deleteButton.type = 'button';
                deleteButton.classList.add('mt-7', 'p-2', 'bg-red-500', 'text-white', 'rounded-md', 'hover:bg-red-600', 'focus:outline-none', 'focus:ring-2', 'focus:ring-offset-2', 'focus:ring-red-500');
                deleteButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg>';
                deleteButton.addEventListener('click', function() {
                    itemDiv.remove();
                });

                itemDiv.appendChild(deleteButton);

                // Add the form to the container
                itemsContainer.appendChild(itemDiv);

                // Increment the index
                index++;
            }

            // Add delete buttons to existing items
            document.querySelectorAll('.item-row').forEach(function(item) {
                const deleteButton = document.createElement('button');
                deleteButton.type = 'button';
                deleteButton.classList.add('mt-7', 'p-2', 'bg-red-500', 'text-white', 'rounded-md', 'hover:bg-red-600', 'focus:outline-none', 'focus:ring-2', 'focus:ring-offset-2', 'focus:ring-red-500');
                deleteButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg>';
                deleteButton.addEventListener('click', function() {
                    item.remove();
                });

                item.appendChild(deleteButton);
            });

            // Le champ destinataire (secteur) est maintenant un simple champ texte

            // Gestion du datalist pour le destinataire utilisateur
            const destinataireUserInput = document.getElementById('destinataire-user-input');
            const destinataireUserHidden = document.getElementById('destinataire-user-hidden');
            const destinataireUserSelect = document.querySelector('select[name="{{ form.destinataireUser.vars.full_name }}"]');
            const datalistOptions = document.querySelectorAll('#destinataire-user-list option');

            // Créer un mapping des noms vers les IDs
            const userMap = {};
            datalistOptions.forEach(option => {
                userMap[option.value] = option.getAttribute('data-id');
            });

            // Mettre à jour le champ caché et le select quand l'utilisateur sélectionne un destinataire
            destinataireUserInput.addEventListener('input', function() {
                const selectedName = this.value;
                const selectedId = userMap[selectedName];

                if (selectedId) {
                    destinataireUserHidden.value = selectedId;
                    // Mettre à jour le select caché
                    if (destinataireUserSelect) {
                        destinataireUserSelect.value = selectedId;
                    }
                } else {
                    destinataireUserHidden.value = '';
                    if (destinataireUserSelect) {
                        destinataireUserSelect.value = '';
                    }
                }
            });

            // Si une valeur est déjà sélectionnée, afficher le nom correspondant
            if (destinataireUserHidden.value) {
                for (const [name, id] of Object.entries(userMap)) {
                    if (id === destinataireUserHidden.value) {
                        destinataireUserInput.value = name;
                        break;
                    }
                }
            }
        });
    </script>
{% endblock %}
