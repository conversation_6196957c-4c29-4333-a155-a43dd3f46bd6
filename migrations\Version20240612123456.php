<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240612123456 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute les champs WBS et dateGestionnaire à la table demande';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE demande ADD wbs VARCHAR(255) DEFAULT NULL, ADD date_gestionnaire DATE DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE demande DROP wbs, DROP date_gestionnaire');
    }
}
