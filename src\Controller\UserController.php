<?php

namespace App\Controller;

use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/user')]
class UserController extends AbstractController
{
    #[Route('/technicians', name: 'app_user_technicians', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function getTechnicians(UserRepository $userRepository): Response
    {
        // Vérifier que la requête est une requête AJAX
        if (!$this->getRequest()->isXmlHttpRequest()) {
            throw $this->createAccessDeniedException('Cette route n\'est accessible qu\'en AJAX.');
        }

        // Récupérer uniquement les utilisateurs qui ont les rôles spécifiques
        $advancedRoles = [
            'ROLE_USER_APPROVISIONNEMENT',
            'ROLE_MANAGER_GAP',
            'ROLE_MANAGER_USINAGE',
            'ROLE_MANAGER_DIRECTION',
            'ROLE_MANAGER_SUPPLY_CHAIN',
            'ROLE_ADMIN'
        ];

        $techniciens = $userRepository->createQueryBuilder('u')
            ->where('u.roles LIKE :role1 OR u.roles LIKE :role2 OR u.roles LIKE :role3 OR u.roles LIKE :role4 OR u.roles LIKE :role5 OR u.roles LIKE :role6 OR u.username = :eerdmann')
            ->andWhere('u.username != :excluded_user')
            ->setParameter('role1', '%"' . $advancedRoles[0] . '"%')
            ->setParameter('role2', '%"' . $advancedRoles[1] . '"%')
            ->setParameter('role3', '%"' . $advancedRoles[2] . '"%')
            ->setParameter('role4', '%"' . $advancedRoles[3] . '"%')
            ->setParameter('role5', '%"' . $advancedRoles[4] . '"%')
            ->setParameter('role6', '%"' . $advancedRoles[5] . '"%')
            ->setParameter('eerdmann', 'eerdmann')
            ->setParameter('excluded_user', 'fkleindienst')
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();

        // Formater les données pour la réponse JSON
        $techniciensData = [];
        foreach ($techniciens as $technicien) {
            $techniciensData[] = [
                'id' => $technicien->getId(),
                'nomComplet' => $technicien->getNomComplet(),
                'username' => $technicien->getUsername(),
            ];
        }

        return new JsonResponse($techniciensData);
    }

    private function getRequest()
    {
        return $this->container->get('request_stack')->getCurrentRequest();
    }
}
