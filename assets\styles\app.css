/* Custom styles */
.btn-primary {
    @apply bg-primary text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary;
}

.btn-secondary {
    @apply bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary;
}

.btn-danger {
    @apply bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500;
}

.form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.card {
    @apply bg-white shadow overflow-hidden sm:rounded-lg;
}

.card-header {
    @apply px-4 py-5 sm:px-6;
}

.card-body {
    @apply px-4 py-5 sm:p-6;
}

.card-footer {
    @apply px-4 py-4 sm:px-6 bg-gray-50;
}

.badge {
    @apply px-2 inline-flex text-xs leading-5 font-semibold rounded-full;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
    @apply bg-red-100 text-red-800;
}

.badge-info {
    @apply bg-blue-100 text-blue-800;
}
