<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250506065441 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE demande ADD destinataire_user_id INT DEFAULT NULL, CHANGE destinataire destinataire VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE demande ADD CONSTRAINT FK_2694D7A5E5A2654E FOREIGN KEY (destinataire_user_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2694D7A5E5A2654E ON demande (destinataire_user_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE demande DROP FOREIGN KEY FK_2694D7A5E5A2654E
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_2694D7A5E5A2654E ON demande
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE demande DROP destinataire_user_id, CHANGE destinataire destinataire VARCHAR(255) NOT NULL
        SQL);
    }
}
