<?php

namespace App\Repository;

use App\Entity\DemandeItem;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<DemandeItem>
 *
 * @method DemandeItem|null find($id, $lockMode = null, $lockVersion = null)
 * @method DemandeItem|null findOneBy(array $criteria, array $orderBy = null)
 * @method DemandeItem[]    findAll()
 * @method DemandeItem[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DemandeItemRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DemandeItem::class);
    }

    public function save(DemandeItem $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(DemandeItem $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
