<?php

namespace App\Controller;

use App\Entity\Demande;
use App\Entity\User;
use App\Repository\DemandeRepository;
use App\Repository\UserRepository;
use App\Service\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/validation-box')]
class ValidationBoxController extends AbstractController
{
    #[Route('/', name: 'app_validation_box_index', methods: ['GET'])]
    public function index(DemandeRepository $demandeRepository, UserRepository $userRepository): Response
    {
        $user = $this->getUser();
        if (!$user->isAdmin()) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour accéder à cette page.');
        }

        $pendingDemandes = $demandeRepository->findPendingDemandes();

        // Récupérer uniquement les utilisateurs qui ont les rôles spécifiques
        $advancedRoles = [
            'ROLE_USER_APPROVISIONNEMENT',
            'ROLE_MANAGER_GAP',
            'ROLE_MANAGER_USINAGE',
            'ROLE_MANAGER_DIRECTION',
            'ROLE_MANAGER_SUPPLY_CHAIN',
            'ROLE_ADMIN'
        ];

        $techniciens = $userRepository->createQueryBuilder('u')
            ->where('u.roles LIKE :role1 OR u.roles LIKE :role2 OR u.roles LIKE :role3 OR u.roles LIKE :role4 OR u.roles LIKE :role5 OR u.roles LIKE :role6 OR u.username = :eerdmann')
            ->andWhere('u.username != :excluded_user')
            ->setParameter('role1', '%"' . $advancedRoles[0] . '"%')
            ->setParameter('role2', '%"' . $advancedRoles[1] . '"%')
            ->setParameter('role3', '%"' . $advancedRoles[2] . '"%')
            ->setParameter('role4', '%"' . $advancedRoles[3] . '"%')
            ->setParameter('role5', '%"' . $advancedRoles[4] . '"%')
            ->setParameter('role6', '%"' . $advancedRoles[5] . '"%')
            ->setParameter('eerdmann', 'eerdmann')
            ->setParameter('excluded_user', 'fkleindienst')
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();

        return $this->render('validation_box/index.html.twig', [
            'demandes' => $pendingDemandes,
            'techniciens' => $techniciens,
        ]);
    }

    #[Route('/validate/{id}', name: 'app_validation_box_validate', methods: ['POST'])]
    public function validate(Request $request, Demande $demande, EntityManagerInterface $entityManager, UserRepository $userRepository, EmailService $emailService): Response
    {
        $user = $this->getUser();
        if (!$user->isAdmin()) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour accéder à cette page.');
        }

        // Vérifier le token CSRF
        if (!$this->isCsrfTokenValid('validate_demande', $request->request->get('_token'))) {
            $this->addFlash('error', 'Erreur de sécurité.');
            return $this->redirectToRoute('app_validation_box_index');
        }

        // Sauvegarder l'ancien statut pour l'email
        $oldStatus = $demande->getStatut();

        // Mettre à jour le statut de la demande
        $demande->setStatut(Demande::STATUT_VALIDEE);

        // Vérifier qu'un technicien est fourni (obligatoire)
        $technicienId = $request->request->get('technicien');
        if (!$technicienId) {
            $this->addFlash('error', 'Sélectionnez un gestionnaire.');
            return $this->redirectToRoute('app_validation_box_index');
        }

        // Assigner le technicien
        $technicien = $userRepository->find($technicienId);
        if (!$technicien) {
            $this->addFlash('error', 'Gestionnaire invalide.');
            return $this->redirectToRoute('app_validation_box_index');
        }

        $demande->setTechnicien($technicien);

        // Ajouter un commentaire si fourni
        $commentaire = $request->request->get('commentaire');
        if ($commentaire) {
            $demande->setCommentaireGestionnaire(
                ($demande->getCommentaireGestionnaire() ? $demande->getCommentaireGestionnaire() . "\n\n" : '') .
                "Validé par " . $this->getUser()->getNomComplet() . " le " . (new \DateTime())->format('d/m/Y H:i') . ":\n" .
                $commentaire
            );
        }

        $entityManager->flush();

        // Envoyer un email pour notifier de l'assignation
        $emailService->sendDemandeAssignedEmail($demande);

        // Envoyer un email pour notifier du changement de statut
        $emailService->sendDemandeStatusChangedEmail($demande, $oldStatus);

        $this->addFlash('success', 'Demande validée et assignée.');

        return $this->redirectToRoute('app_validation_box_index');
    }

    #[Route('/reject/{id}', name: 'app_validation_box_reject', methods: ['POST'])]
    public function reject(Request $request, Demande $demande, EntityManagerInterface $entityManager, EmailService $emailService): Response
    {
        $user = $this->getUser();
        if (!$user->isAdmin()) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour accéder à cette page.');
        }

        // Vérifier le token CSRF
        if (!$this->isCsrfTokenValid('reject_demande', $request->request->get('_token'))) {
            $this->addFlash('error', 'Erreur de sécurité.');
            return $this->redirectToRoute('app_validation_box_index');
        }

        // Sauvegarder l'ancien statut pour l'email
        $oldStatus = $demande->getStatut();

        // Mettre à jour le statut de la demande
        $demande->setStatut(Demande::STATUT_REFUSEE);

        // Ajouter un commentaire si fourni
        $commentaire = $request->request->get('commentaire');
        if ($commentaire) {
            $demande->setCommentaireGestionnaire(
                ($demande->getCommentaireGestionnaire() ? $demande->getCommentaireGestionnaire() . "\n\n" : '') .
                "Refusé par " . $this->getUser()->getNomComplet() . " le " . (new \DateTime())->format('d/m/Y H:i') . ":\n" .
                $commentaire
            );
        }

        $entityManager->flush();

        // Envoyer un email pour notifier du changement de statut
        $emailService->sendDemandeStatusChangedEmail($demande, $oldStatus);

        $this->addFlash('success', 'Demande refusée.');

        return $this->redirectToRoute('app_validation_box_index');
    }

    #[Route('/count', name: 'app_validation_box_count', methods: ['GET'])]
    public function count(DemandeRepository $demandeRepository): Response
    {
        $user = $this->getUser();
        if (!$user->isAdmin()) {
            return $this->json(['count' => 0]);
        }

        $count = $demandeRepository->countPendingDemandes();

        return $this->json(['count' => $count]);
    }
}
