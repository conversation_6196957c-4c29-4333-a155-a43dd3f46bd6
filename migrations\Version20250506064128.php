<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250506064128 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE demande ADD personne_concernee_id INT DEFAULT NULL, ADD technicien_id INT DEFAULT NULL, DROP personne_concernee
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE demande ADD CONSTRAINT FK_2694D7A544B687BD FOREIGN KEY (personne_concernee_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE demande ADD CONSTRAINT FK_2694D7A513457256 FOREIGN KEY (technicien_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2694D7A544B687BD ON demande (personne_concernee_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2694D7A513457256 ON demande (technicien_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE demande DROP FOREIGN KEY FK_2694D7A544B687BD
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE demande DROP FOREIGN KEY FK_2694D7A513457256
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_2694D7A544B687BD ON demande
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_2694D7A513457256 ON demande
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE demande ADD personne_concernee VARCHAR(255) NOT NULL, DROP personne_concernee_id, DROP technicien_id
        SQL);
    }
}
