<?php

namespace App\Command;

use App\Entity\Service;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Service\LdapService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:create-users-from-ldap',
    description: 'Crée des utilisateurs dans la base de données à partir des informations LDAP',
)]
class CreateUsersFromLdapCommand extends Command
{
    private UserRepository $userRepository;
    private LdapService $ldapService;
    private EntityManagerInterface $entityManager;

    public function __construct(
        UserRepository $userRepository,
        LdapService $ldapService,
        EntityManagerInterface $entityManager
    ) {
        parent::__construct();
        $this->userRepository = $userRepository;
        $this->ldapService = $ldapService;
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'dry-run',
                null,
                InputOption::VALUE_NONE,
                'Simuler l\'exécution sans créer d\'utilisateurs'
            )
            ->addOption(
                'limit',
                null,
                InputOption::VALUE_REQUIRED,
                'Limiter le nombre d\'utilisateurs à créer',
                0
            )
            ->addOption(
                'department',
                null,
                InputOption::VALUE_REQUIRED,
                'Filtrer par département'
            )
            ->addOption(
                'with-mobile-only',
                null,
                InputOption::VALUE_NONE,
                'Ne créer que les utilisateurs ayant un numéro de mobile'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Création d\'utilisateurs depuis LDAP');

        // Options
        $dryRun = $input->getOption('dry-run');
        $limit = (int)$input->getOption('limit');
        $department = $input->getOption('department');
        $withMobileOnly = $input->getOption('with-mobile-only');

        if ($dryRun) {
            $io->note('Mode simulation activé - aucun utilisateur ne sera créé');
        }

        // Récupérer tous les utilisateurs du LDAP
        $io->section('Récupération des utilisateurs depuis LDAP');
        $ldapUsers = $this->ldapService->getAllUsersInfo();
        $io->info(sprintf('Nombre total d\'utilisateurs trouvés dans LDAP : %d', count($ldapUsers)));

        // Filtrer les utilisateurs selon les options
        $filteredUsers = $this->filterUsers($ldapUsers, $department, $withMobileOnly);
        $io->info(sprintf('Nombre d\'utilisateurs après filtrage : %d', count($filteredUsers)));

        // Limiter le nombre d'utilisateurs si nécessaire
        if ($limit > 0 && count($filteredUsers) > $limit) {
            $filteredUsers = array_slice($filteredUsers, 0, $limit);
            $io->info(sprintf('Limitation à %d utilisateurs', $limit));
        }

        // Compteurs
        $createdCount = 0;
        $skippedCount = 0;
        $errorCount = 0;

        // Barre de progression
        $io->progressStart(count($filteredUsers));

        foreach ($filteredUsers as $ldapUserInfo) {
            try {
                $username = $ldapUserInfo['username'] ?? null;
                
                if (!$username) {
                    $io->warning("Utilisateur sans nom d'utilisateur, ignoré.");
                    $skippedCount++;
                    $io->progressAdvance();
                    continue;
                }

                // Vérifier si l'utilisateur existe déjà
                $existingUser = $this->userRepository->findOneBy(['username' => $username]);
                
                if ($existingUser) {
                    $skippedCount++;
                    $io->progressAdvance();
                    continue;
                }

                // Créer un nouvel utilisateur
                if (!$dryRun) {
                    $this->createUserFromLdapInfo($ldapUserInfo);
                }
                
                $createdCount++;
                $io->progressAdvance();
                
            } catch (\Exception $e) {
                $errorCount++;
                $username = $ldapUserInfo['username'] ?? 'inconnu';
                $io->error("Erreur lors de la création de l'utilisateur {$username}: {$e->getMessage()}");
                $io->progressAdvance();
            }
        }

        $io->progressFinish();

        // Résumé
        if ($dryRun) {
            $io->success(sprintf(
                "Simulation terminée : %d utilisateurs seraient créés, %d ignorés (déjà existants), %d erreurs.",
                $createdCount,
                $skippedCount,
                $errorCount
            ));
        } else {
            $io->success(sprintf(
                "Création terminée : %d utilisateurs créés, %d ignorés (déjà existants), %d erreurs.",
                $createdCount,
                $skippedCount,
                $errorCount
            ));
        }

        return Command::SUCCESS;
    }

    /**
     * Filtre les utilisateurs LDAP selon les critères spécifiés
     */
    private function filterUsers(array $users, ?string $department, bool $withMobileOnly): array
    {
        return array_filter($users, function ($user) use ($department, $withMobileOnly) {
            // Filtrer par département si spécifié
            if ($department && ($user['department'] ?? '') !== $department) {
                return false;
            }

            // Filtrer par présence de numéro mobile si demandé
            if ($withMobileOnly && empty($user['mobile'])) {
                return false;
            }

            // Vérifier que l'utilisateur a un email (critère de base)
            if (empty($user['email'])) {
                return false;
            }

            // Exclure les comptes spéciaux ou de service si nécessaire
            // Par exemple, exclure les comptes admin ou les comptes de service
            if (isset($user['email']) && $user['email'] === '<EMAIL>') {
                return false;
            }

            return true;
        });
    }

    /**
     * Crée un utilisateur à partir des informations LDAP
     */
    private function createUserFromLdapInfo(array $ldapUserInfo): User
    {
        // Vérifier si l'utilisateur est dans le groupe VPN
        $isInVpn = $this->ldapService->isUserInVpn(
            $ldapUserInfo['username'], 
            'VPN_SSL', 
            'OU=FRSCM_Groupes,DC=scmlemans,DC=com'
        );

        // Vérifier si le service existe, sinon le créer
        $secteurRepository = $this->entityManager->getRepository(Service::class);
        $secteur = $secteurRepository->findOneBy(['name' => $ldapUserInfo['department']]);

        if (!$secteur && isset($ldapUserInfo['department'])) {
            $secteur = new Service();
            $secteur->setName($ldapUserInfo['department']);
            $this->entityManager->persist($secteur);
            $this->entityManager->flush();
        }

        // Extraire le nom et prénom du DN
        $nom = null;
        $prenom = null;
        $nomComplet = preg_match('/CN=([^,]+)/', $ldapUserInfo['distinguishedName'], $matches);
        if ($nomComplet) {
            list($nom, $prenom) = explode(' ', $matches[1], 2);
        }

        // Déterminer les rôles
        $department = $this->normalizeDepartment($ldapUserInfo['department'] ?? 'DEFAULT');
        $roles = $ldapUserInfo['isManager']
            ? ['ROLE_MANAGER_' . $department]
            : ['ROLE_USER_' . $department];

        // Créer un nouvel utilisateur
        $newUser = new User();
        $newUser->setUsername($ldapUserInfo['username']);
        $newUser->setEmail($ldapUserInfo['email'] ?? null);
        $newUser->setNom($nom ?? null);
        $newUser->setPrenom($prenom ?? null);
        $newUser->setRoles($roles);
        $newUser->setSecteur($ldapUserInfo['department'] ?? 'null');
        $newUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? null);
        $newUser->setIsManager($ldapUserInfo['isManager'] ?? null);
        $newUser->setTitre($ldapUserInfo['title'] ?? null);
        $newUser->setVpn($isInVpn);
        $newUser->setMobile($ldapUserInfo['mobile'] ?? null);
        $newUser->setTelephoneNumber($ldapUserInfo['telephoneNumber'] ?? null);
        
        $this->entityManager->persist($newUser);
        $this->entityManager->flush();
        
        return $newUser;
    }

    /**
     * Normalise le nom du département pour les rôles
     */
    private function normalizeDepartment(?string $department): string
    {
        if (empty($department)) {
            return 'DEFAULT';
        }
        // Remplace les espaces ou caractères spéciaux par des underscores
        $normalized = preg_replace('/[^A-Z0-9]/', '_', strtoupper($department));

        return $normalized;
    }
}

