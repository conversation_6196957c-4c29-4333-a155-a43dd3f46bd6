<?php

namespace App\Form;

use App\Entity\Demande;
use App\Entity\User;
use App\Repository\UserRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\UrlType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Doctrine\ORM\EntityManagerInterface;

class DemandeType extends AbstractType
{
    private $userRepository;
    private $entityManager;

    public function __construct(UserRepository $userRepository, EntityManagerInterface $entityManager)
    {
        $this->userRepository = $userRepository;
        $this->entityManager = $entityManager;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('dateSouhaitee', DateType::class, [
                'widget' => 'single_text',
                'label' => 'Date souhaitée de réception',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                    'min' => (new \DateTime())->format('Y-m-d'),
                ],
            ])

            ->add('urgence', ChoiceType::class, [
                'label' => 'Urgence',
                'choices' => [
                    'Basse' => Demande::URGENCE_BASSE,
                    'Moyenne' => Demande::URGENCE_MOYENNE,
                    'Haute' => Demande::URGENCE_HAUTE,
                ],
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                ],
            ])
            ->add('nature', ChoiceType::class, [
                'label' => 'Nature de la demande',
                'choices' => [
                    'Échantillons' => Demande::NATURE_ECHANTILLONS,
                    'Outillage' => Demande::NATURE_OUTILLAGE,
                    'Pièces' => Demande::NATURE_PIECES,
                ],
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                ],
            ])
            ->add('compte', TextType::class, [
                'label' => 'Compte (numéro de centre de coût)',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                    'placeholder' => 'Ex: 137654',
                ],
            ])
            ->add('wbs', TextType::class, [
                'label' => 'WBS',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                    'placeholder' => 'Ex: WBS123456',
                ],
            ])
            ->add('destinataire', ChoiceType::class, [
                'label' => 'Destinataire (secteur)',
                'required' => false,
                'choices' => $this->getSecteurChoices(),
                'placeholder' => 'Sélectionner un secteur',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                ],
            ])
            ->add('destinataireUser', EntityType::class, [
                'class' => User::class,
                'choice_label' => 'nomComplet',
                'label' => 'Destinataire (utilisateur)',
                'required' => false,
                'placeholder' => 'Sélectionner un utilisateur',
                'query_builder' => function (UserRepository $er) {
                    return $er->createQueryBuilder('u')
                        ->orderBy('u.nom', 'ASC')
                        ->addOrderBy('u.prenom', 'ASC');
                },
                'attr' => [
                    'class' => 'hidden',
                    'style' => 'display: none !important; height: 0; width: 0; position: absolute; left: -9999px;',
                ],
            ])
            ->add('lienPlan', UrlType::class, [
                'label' => 'Lien vers le plan',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                    'placeholder' => 'Ex: https://example.com/plans/123',
                ],
            ])
            ->add('potentiel', ChoiceType::class, [
                'label' => 'Potentiel',
                'required' => false,
                'choices' => [
                    'One Shot' => Demande::POTENTIEL_ONE_SHOT,
                    'Récurrent' => Demande::POTENTIEL_RECURRENT,
                ],
                'placeholder' => 'Sélectionner le potentiel',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                ],
            ])
            ->add('nomClient', TextType::class, [
                'label' => 'Nom du client',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                    'placeholder' => 'Ex: Entreprise XYZ',
                ],
            ])
            ->add('gestionnairePreferentiel', EntityType::class, [
                'class' => User::class,
                'choice_label' => 'nomComplet',
                'label' => 'Gestionnaire préférentiel',
                'required' => false,
                'placeholder' => 'Sélectionner un gestionnaire',
                'query_builder' => function (UserRepository $er) {
                    return $er->createQueryBuilder('u')
                        ->where('u.roles LIKE :role1 OR u.roles LIKE :role2 OR u.roles LIKE :role3 OR u.roles LIKE :role4 OR u.roles LIKE :role5')
                        ->setParameter('role1', '%ROLE_USER_APPROVISIONNEMENT%')
                        ->setParameter('role2', '%ROLE_MANAGER_GAP%')
                        ->setParameter('role3', '%ROLE_MANAGER_USINAGE%')
                        ->setParameter('role4', '%ROLE_MANAGER_DIRECTION%')
                        ->setParameter('role5', '%ROLE_MANAGER_SUPPLY_CHAIN%')
                        ->orderBy('u.nom', 'ASC')
                        ->addOrderBy('u.prenom', 'ASC');
                },
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                ],
            ])
            ->add('aSortirEnAchat', CheckboxType::class, [
                'label' => 'À sortir en Achat',
                'required' => false,
                'attr' => [
                    'class' => 'rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50',
                ],
            ])
            ->add('commentaireDemandeur', TextareaType::class, [
                'label' => 'Commentaire du demandeur',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                    'rows' => 3,
                    'placeholder' => 'Informations complémentaires sur la demande...',
                ],
            ])
            ->add('items', CollectionType::class, [
                'entry_type' => DemandeItemType::class,
                'entry_options' => ['label' => false],
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'label' => false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Demande::class,
        ]);
    }

    /**
     * Récupère les secteurs distincts pour le champ destinataire
     */
    private function getSecteurChoices(): array
    {
        $secteurs = $this->userRepository->findAllDistinctSecteurs();
        $choices = [];

        foreach ($secteurs as $secteur) {
            $choices[$secteur] = $secteur;
        }

        return $choices;
    }
}
