{{ form_start(form, {'attr': {'class': 'space-y-8'}}) }}
    <!-- Section Informations générales -->
    <div class="form-section mb-6">
        <div class="form-section-header">
            <h3 class="text-lg font-medium text-gray-800">
                <i class="fas fa-info-circle mr-2 text-primary"></i>
                Informations générales
            </h3>
        </div>
        <div class="form-section-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    {{ form_label(form.dateSouhaitee, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.dateSouhaitee, {'attr': {'class': 'form-input'}}) }}
                    {{ form_errors(form.dateSouhaitee, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    {{ form_label(form.urgence, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.urgence, {'attr': {'class': 'form-select'}}) }}
                    {{ form_errors(form.urgence, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    {{ form_label(form.nature, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.nature, {'attr': {'class': 'form-select'}}) }}
                    {{ form_errors(form.nature, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    {{ form_label(form.compte, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.compte, {'attr': {'class': 'form-input'}}) }}
                    {{ form_errors(form.compte, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    {{ form_label(form.potentiel, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.potentiel, {'attr': {'class': 'form-select'}}) }}
                    {{ form_errors(form.potentiel, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    {{ form_label(form.nomClient, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.nomClient, {'attr': {'class': 'form-input'}}) }}
                    {{ form_errors(form.nomClient, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    {{ form_label(form.lienPlan, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.lienPlan, {'attr': {'class': 'form-input'}}) }}
                    {{ form_errors(form.lienPlan, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>
            </div>
        </div>
    </div>

    <!-- Section Destinataire -->
    <div class="form-section mb-6">
        <div class="form-section-header">
            <h3 class="text-lg font-medium text-gray-800">
                <i class="fas fa-user mr-2 text-primary"></i>
                Destinataire
            </h3>
        </div>
        <div class="form-section-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    {{ form_label(form.destinataire, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.destinataire, {'attr': {'class': 'form-input'}}) }}
                    {{ form_errors(form.destinataire, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    <label for="destinataire-user-input" class="block text-sm font-medium text-gray-700 mb-1">{{ form_label(form.destinataireUser) }}</label>
                    <input type="text" id="destinataire-user-input" list="destinataire-user-list" placeholder="Rechercher un utilisateur..." class="form-input">
                    <datalist id="destinataire-user-list">
                        {% for user in form.destinataireUser.vars.choices %}
                            <option value="{{ user.label }}" data-id="{{ user.value }}">
                        {% endfor %}
                    </datalist>
                    <input type="hidden" id="destinataire-user-hidden" name="{{ form.destinataireUser.vars.full_name }}" value="{{ form.destinataireUser.vars.value }}">
                    {{ form_widget(form.destinataireUser, {'attr': {'class': 'hidden'}}) }}
                    {{ form_errors(form.destinataireUser, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>
            </div>
        </div>
    </div>

    <!-- Section Commentaire -->
    <div class="form-section mb-6">
        <div class="form-section-header">
            <h3 class="text-lg font-medium text-gray-800">
                <i class="fas fa-comment mr-2 text-primary"></i>
                Commentaire
            </h3>
        </div>
        <div class="form-section-body">
            <div class="form-group">
                {{ form_label(form.commentaireDemandeur, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                {{ form_widget(form.commentaireDemandeur, {'attr': {'class': 'form-textarea', 'rows': 4}}) }}
                {{ form_errors(form.commentaireDemandeur, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
            </div>
        </div>
    </div>

    <!-- Section Items -->
    <div class="form-section mb-6">
        <div class="form-section-header">
            <h3 class="text-lg font-medium text-gray-800">
                <i class="fas fa-boxes mr-2 text-primary"></i>
                Items
            </h3>
            <p class="text-sm text-gray-500 mt-1">Ajoutez les références et quantités des items demandés</p>
        </div>
        <div class="form-section-body">
            <div id="items-container" class="space-y-3" data-prototype="{{ form_widget(form.items.vars.prototype)|e('html_attr') }}">
                {% for item in form.items %}
                    <div class="item-row flex items-start space-x-3 mb-3 p-4 border border-gray-200 rounded-lg">
                        <div class="flex-1">
                            {{ form_label(item.reference, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                            {{ form_widget(item.reference, {'attr': {'class': 'form-input'}}) }}
                            {{ form_errors(item.reference, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                        </div>
                        <div class="w-32">
                            {{ form_label(item.quantite, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                            {{ form_widget(item.quantite, {'attr': {'class': 'form-input'}}) }}
                            {{ form_errors(item.quantite, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                        </div>
                    </div>
                {% endfor %}
            </div>

            <button type="button" id="add-item" class="btn btn-success mt-4">
                <i class="fas fa-plus mr-2"></i>
                Ajouter un item
            </button>
        </div>
    </div>

    <!-- Boutons d'action -->
    <div class="pt-4 border-t border-gray-200">
        <div class="flex justify-end space-x-3">
            <a href="{{ path('app_demande_index') }}" class="btn btn-secondary">
                Annuler
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save mr-2"></i>
                {{ button_label|default('Enregistrer') }}
            </button>
        </div>
    </div>
{{ form_end(form) }}
