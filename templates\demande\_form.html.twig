{{ form_start(form, {'attr': {'class': 'space-y-8'}}) }}
    <!-- Section Informations générales -->
    <div class="form-section mb-6">
        <div class="form-section-header">
            <h3 class="text-lg font-medium text-gray-800">
                <i class="fas fa-info-circle mr-2 text-primary"></i>
                Informations générales
            </h3>
        </div>
        <div class="form-section-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    {{ form_label(form.dateSouhaitee, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.dateSouhaitee, {'attr': {'class': 'form-input'}}) }}
                    {{ form_errors(form.dateSouhaitee, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    {{ form_label(form.urgence, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.urgence, {'attr': {'class': 'form-select'}}) }}
                    {{ form_errors(form.urgence, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    {{ form_label(form.nature, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.nature, {'attr': {'class': 'form-select'}}) }}
                    {{ form_errors(form.nature, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            {{ form_label(form.compte, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                            {{ form_widget(form.compte, {'attr': {'class': 'form-input'}}) }}
                            {{ form_errors(form.compte, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                        </div>
                        <div>
                            {{ form_label(form.wbs, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                            {{ form_widget(form.wbs, {'attr': {'class': 'form-input'}}) }}
                            {{ form_errors(form.wbs, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Au moins l'un des champs "Centre de coût" ou "WBS" doit être renseigné</p>
                </div>

                <div class="form-group">
                    {{ form_label(form.potentiel, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.potentiel, {'attr': {'class': 'form-select'}}) }}
                    {{ form_errors(form.potentiel, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    {{ form_label(form.nomClient, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.nomClient, {'attr': {'class': 'form-input'}}) }}
                    {{ form_errors(form.nomClient, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>



                <div class="form-group">
                    {{ form_label(form.gestionnairePreferentiel, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.gestionnairePreferentiel, {'attr': {'class': 'form-select'}}) }}
                    {{ form_errors(form.gestionnairePreferentiel, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>


            </div>
        </div>
    </div>

    <!-- Section Destinataire -->
    <div class="form-section mb-6">
        <div class="form-section-header">
            <h3 class="text-lg font-medium text-gray-800">
                <i class="fas fa-user mr-2 text-primary"></i>
                Destinataire
            </h3>
        </div>
        <div class="form-section-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    {{ form_label(form.destinataire, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.destinataire, {'attr': {'class': 'form-input'}}) }}
                    {{ form_errors(form.destinataire, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    <label for="destinataire-user-input" class="block text-sm font-medium text-gray-700 mb-1">{{ form_label(form.destinataireUser) }}</label>
                    <input type="text" id="destinataire-user-input" list="destinataire-user-list" placeholder="Rechercher un utilisateur..." class="form-input">
                    <datalist id="destinataire-user-list">
                        {% for user in form.destinataireUser.vars.choices %}
                            <option value="{{ user.label }}" data-id="{{ user.value }}">
                        {% endfor %}
                    </datalist>
                    <input type="hidden" id="destinataire-user-hidden" name="{{ form.destinataireUser.vars.full_name }}" value="{{ form.destinataireUser.vars.value }}">
                    {{ form_widget(form.destinataireUser, {'attr': {'class': 'hidden'}}) }}
                    {{ form_errors(form.destinataireUser, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>
            </div>
        </div>
    </div>

    <!-- Section Commentaire -->
    <div class="form-section mb-6">
        <div class="form-section-header">
            <h3 class="text-lg font-medium text-gray-800">
                <i class="fas fa-comment mr-2 text-primary"></i>
                Commentaire
            </h3>
        </div>
        <div class="form-section-body">
            <div class="form-group">
                {{ form_label(form.commentaireDemandeur, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                {{ form_widget(form.commentaireDemandeur, {'attr': {'class': 'form-textarea', 'rows': 4}}) }}
                {{ form_errors(form.commentaireDemandeur, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
            </div>
        </div>
    </div>

    <!-- Section Items -->
    <div class="form-section mb-6">
        <div class="form-section-header">
            <h3 class="text-lg font-medium text-gray-800">
                <i class="fas fa-boxes mr-2 text-primary"></i>
                Items
            </h3>
            <p class="text-sm text-gray-500 mt-1">Ajoutez les références et quantités des items demandés</p>
        </div>
        <div class="form-section-body">
            <div id="items-container" class="space-y-3" data-prototype="{{ form_widget(form.items.vars.prototype)|e('html_attr') }}">
                {% for item in form.items %}
                    <div class="item-row p-4 border border-gray-200 rounded-lg mb-3" data-item-index="{{ loop.index0 }}">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div>
                                {{ form_label(item.reference, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                                {{ form_widget(item.reference, {'attr': {'class': 'form-input'}}) }}
                                {{ form_errors(item.reference, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                            </div>
                            <div>
                                {{ form_label(item.quantite, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                                {{ form_widget(item.quantite, {'attr': {'class': 'form-input'}}) }}
                                {{ form_errors(item.quantite, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                            </div>
                            <div>
                                {{ form_label(item.dateSouhaitee, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                                {{ form_widget(item.dateSouhaitee, {'attr': {'class': 'form-input'}}) }}
                                {{ form_errors(item.dateSouhaitee, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                            </div>
                            <div>
                                {{ form_label(item.lienPlan, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                                {{ form_widget(item.lienPlan, {'attr': {'class': 'form-input'}}) }}
                                {{ form_errors(item.lienPlan, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                            </div>
                        </div>
                        {% if loop.index0 > 0 %}
                            <button type="button" class="remove-item btn btn-danger btn-sm mt-3">
                                <i class="fas fa-trash mr-1"></i>
                                Supprimer
                            </button>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>

            <button type="button" id="add-item" class="btn btn-success mt-4">
                <i class="fas fa-plus mr-2"></i>
                Ajouter un item
            </button>
        </div>
    </div>

    <!-- Boutons d'action -->
    <div class="pt-4 border-t border-gray-200">
        <div class="flex justify-end space-x-3">
            <a href="{{ path('app_demande_index') }}" class="btn btn-secondary">
                Annuler
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save mr-2"></i>
                {{ button_label|default('Enregistrer') }}
            </button>
        </div>
    </div>
{{ form_end(form) }}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const itemsContainer = document.getElementById('items-container');
    const addItemButton = document.getElementById('add-item');
    let itemIndex = itemsContainer.children.length;

    // Fonction pour ajouter un nouvel item
    addItemButton.addEventListener('click', function() {
        const prototype = itemsContainer.getAttribute('data-prototype');
        const newItem = prototype.replace(/__name__/g, itemIndex);

        const itemDiv = document.createElement('div');
        itemDiv.className = 'item-row p-4 border border-gray-200 rounded-lg mb-3';
        itemDiv.setAttribute('data-item-index', itemIndex);
        itemDiv.innerHTML = newItem;

        // Ajouter le bouton supprimer pour les nouveaux items
        const removeButton = document.createElement('button');
        removeButton.type = 'button';
        removeButton.className = 'remove-item btn btn-danger btn-sm mt-3';
        removeButton.innerHTML = '<i class="fas fa-trash mr-1"></i> Supprimer';
        itemDiv.appendChild(removeButton);

        // Restructurer le contenu pour correspondre au layout
        const formFields = itemDiv.querySelector('.form-widget');
        if (formFields) {
            const gridDiv = document.createElement('div');
            gridDiv.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4';

            // Déplacer les champs dans la grille
            const fields = formFields.children;
            for (let i = 0; i < fields.length; i++) {
                const fieldDiv = document.createElement('div');
                fieldDiv.appendChild(fields[i].cloneNode(true));
                gridDiv.appendChild(fieldDiv);
            }

            itemDiv.innerHTML = '';
            itemDiv.appendChild(gridDiv);
            itemDiv.appendChild(removeButton);
        }

        itemsContainer.appendChild(itemDiv);
        itemIndex++;

        // Ajouter l'événement de suppression
        removeButton.addEventListener('click', function() {
            itemDiv.remove();
        });
    });

    // Fonction pour supprimer un item existant
    itemsContainer.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-item') || e.target.closest('.remove-item')) {
            const itemRow = e.target.closest('.item-row');
            if (itemRow && itemRow.getAttribute('data-item-index') !== '0') {
                itemRow.remove();
            }
        }
    });
});
</script>
