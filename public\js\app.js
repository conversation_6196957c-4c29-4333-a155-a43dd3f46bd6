// Main JavaScript file

document.addEventListener('DOMContentLoaded', function() {
    console.log('Application JavaScript loaded');

    // Initialize filter functionality if the filter form exists
    initializeFilters();

    // Initialize demande item functionality if the form exists
    initializeDemandeItems();
});

// Filter functionality
function initializeFilters() {
    const filterForm = document.getElementById('filter-form');
    if (!filterForm) return;

    const statut = document.getElementById('statut');
    const urgence = document.getElementById('urgence');
    const nature = document.getElementById('nature');
    const search = document.getElementById('search');

    if (statut) statut.addEventListener('change', performFilter);
    if (urgence) urgence.addEventListener('change', performFilter);
    if (nature) nature.addEventListener('change', performFilter);

    let timeout = null;
    if (search) {
        search.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(performFilter, 300);
        });
    }

    function performFilter() {
        const formData = new FormData(filterForm);
        const params = new URLSearchParams();

        for (const [key, value] of formData.entries()) {
            if (value) {
                params.append(key, value);
            }
        }

        const url = `${filterForm.action}?${params.toString()}`;

        fetch(url, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            document.getElementById('demandes-list').innerHTML = html;

            // Update URL without reloading the page
            history.pushState({}, '', url);
        })
        .catch(error => console.error('Error:', error));
    }
}

// Demande item functionality
function initializeDemandeItems() {
    const addItemButton = document.getElementById('add-item-button');
    if (!addItemButton) return;

    const itemsContainer = document.getElementById('items-container');
    const itemTemplate = document.getElementById('item-template');

    if (!itemsContainer || !itemTemplate) return;

    let index = document.querySelectorAll('.item-row').length;

    addItemButton.addEventListener('click', function(event) {
        event.preventDefault();

        const prototype = itemTemplate.dataset.prototype;
        const newForm = prototype.replace(/__name__/g, index);

        // Create a temporary div to parse the form
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = newForm;

        // Create the main item div
        const itemDiv = document.createElement('div');
        itemDiv.classList.add('item-row', 'flex', 'items-start', 'space-x-2', 'mb-3', 'p-3', 'border', 'border-gray-200', 'rounded-md');

        // Create reference div
        const referenceDiv = document.createElement('div');
        referenceDiv.classList.add('flex-1');

        // Find reference label, input and errors
        const referenceLabel = tempDiv.querySelector('label[for$="_reference"]');
        const referenceInput = tempDiv.querySelector('input[id$="_reference"]');
        const referenceErrors = tempDiv.querySelector('div:has(input[id$="_reference"]) + div.invalid-feedback');

        if (referenceLabel) referenceDiv.appendChild(referenceLabel);
        if (referenceInput) referenceDiv.appendChild(referenceInput);
        if (referenceErrors) referenceDiv.appendChild(referenceErrors);

        // Create quantity div
        const quantiteDiv = document.createElement('div');
        quantiteDiv.classList.add('w-24');

        // Find quantity label, input and errors
        const quantiteLabel = tempDiv.querySelector('label[for$="_quantite"]');
        const quantiteInput = tempDiv.querySelector('input[id$="_quantite"]');
        const quantiteErrors = tempDiv.querySelector('div:has(input[id$="_quantite"]) + div.invalid-feedback');

        if (quantiteLabel) quantiteDiv.appendChild(quantiteLabel);
        if (quantiteInput) quantiteDiv.appendChild(quantiteInput);
        if (quantiteErrors) quantiteDiv.appendChild(quantiteErrors);

        // Add the divs to the item row
        itemDiv.appendChild(referenceDiv);
        itemDiv.appendChild(quantiteDiv);

        itemsContainer.appendChild(itemDiv);

        // Add remove button functionality
        const removeButton = itemDiv.querySelector('.remove-item');
        if (removeButton) {
            removeButton.addEventListener('click', function(e) {
                e.preventDefault();
                itemDiv.remove();
            });
        }

        index++;
    });

    // Add event listeners to existing remove buttons
    document.querySelectorAll('.remove-item').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            this.closest('.item-row').remove();
        });
    });
}

// CSRF Protection
function generateCsrfToken(formElement) {
    const csrfField = formElement.querySelector('input[name="_csrf_token"]');
    if (!csrfField) return;

    // Add CSRF token handling if needed
}

// Add form submission event listener for CSRF protection
document.addEventListener('submit', function(event) {
    generateCsrfToken(event.target);
}, true);
