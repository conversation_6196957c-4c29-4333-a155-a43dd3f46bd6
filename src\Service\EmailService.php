<?php

namespace App\Service;

use App\Entity\Demande;
use App\Entity\User;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use Twig\Environment;

class EmailService
{
    private string $senderEmail;
    private string $senderName;
    private $userRepository;
    private $twig;

    public function __construct(
        \App\Repository\UserRepository $userRepository,
        Environment $twig,
        string $senderEmail = '<EMAIL>',
        string $senderName = 'Demandes Track'
    ) {
        $this->userRepository = $userRepository;
        $this->twig = $twig;
        $this->senderEmail = $senderEmail;
        $this->senderName = $senderName;
    }

    /**
     * Crée une instance configurée de PHPMailer
     */
    private function createMailer(): PHPMailer
    {
        $mail = new PHPMailer(true);
        $mail->IsSMTP();
        $mail->Host = 'smtp.office365.com';
        $mail->Port = 587;
        $mail->SMTPAuth = true;

        if($mail->SMTPAuth){
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Username   = '<EMAIL>';
            $mail->Password   = 'bNEsp7M71Hhdp6MqlekUlw3TZduKHL';
        }
        $mail->CharSet = 'UTF-8';
        $mail->smtpConnect();
        $mail->From = '<EMAIL>';
        $mail->FromName = $this->senderName;

        return $mail;
    }

    /**
     * Envoie un email lors de la création d'une demande
     */
    public function sendDemandeCreatedEmail(Demande $demande): void
    {
        try {
            // Envoyer un email au demandeur
            $mail = $this->createMailer();
            $mail->addAddress($demande->getDemandeur()->getEmail(), $demande->getDemandeur()->getNomComplet());
            $mail->Subject = 'Votre demande d\'achat a été créée - #' . $demande->getId();

            // Générer le contenu HTML avec Twig
            $htmlContent = $this->twig->render('emails/demande_created.html.twig', [
                'demande' => $demande,
                'destinataire' => $demande->getDemandeur()->getNomComplet(), // Remplacer app.user par un paramètre explicite
            ]);

            $mail->isHTML(true);
            $mail->Body = $htmlContent;
            $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $htmlContent));

            $mail->send();

            // Envoyer un email uniquement à ffournier et eerdmann
            $this->sendEmailToSpecificUsers(
                'Nouvelle demande d\'achat à valider - #' . $demande->getId(),
                'emails/demande_created_admin.html.twig',
                ['demande' => $demande],
                ['ffournier', 'eerdmann']
            );
        } catch (Exception $e) {
            // Log l'erreur
            error_log('Erreur lors de l\'envoi de l\'email de création de demande: ' . $e->getMessage());
        }
    }

    /**
     * Envoie un email lors de l'assignation d'une demande à un gestionnaire
     */
    public function sendDemandeAssignedEmail(Demande $demande): void
    {
        // Vérifier que la demande a un gestionnaire
        if (!$demande->getTechnicien()) {
            return;
        }

        try {
            // Nous devons envoyer deux emails distincts car les templates et les sujets sont différents

            // 1. Email au gestionnaire
            $mailGestionnaire = $this->createMailer();
            $mailGestionnaire->addAddress($demande->getTechnicien()->getEmail(), $demande->getTechnicien()->getNomComplet());
            $mailGestionnaire->Subject = 'Une demande d\'achat vous a été assignée - #' . $demande->getId();

            // Générer le contenu HTML avec Twig
            $htmlContentGestionnaire = $this->twig->render('emails/demande_assigned.html.twig', [
                'demande' => $demande,
                'destinataire' => $demande->getTechnicien()->getNomComplet(), // Remplacer app.user par un paramètre explicite
            ]);

            $mailGestionnaire->isHTML(true);
            $mailGestionnaire->Body = $htmlContentGestionnaire;
            $mailGestionnaire->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $htmlContentGestionnaire));

            $mailGestionnaire->send();

            // 2. Email au demandeur
            $mailDemandeur = $this->createMailer();
            $mailDemandeur->addAddress($demande->getDemandeur()->getEmail(), $demande->getDemandeur()->getNomComplet());
            $mailDemandeur->Subject = 'Votre demande d\'achat a été validée - #' . $demande->getId();

            // Générer le contenu HTML avec Twig
            $htmlContentDemandeur = $this->twig->render('emails/demande_validated.html.twig', [
                'demande' => $demande,
                'destinataire' => $demande->getDemandeur()->getNomComplet(), // Remplacer app.user par un paramètre explicite
            ]);

            $mailDemandeur->isHTML(true);
            $mailDemandeur->Body = $htmlContentDemandeur;
            $mailDemandeur->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $htmlContentDemandeur));

            $mailDemandeur->send();
        } catch (Exception $e) {
            // Log l'erreur
            error_log('Erreur lors de l\'envoi de l\'email d\'assignation de demande: ' . $e->getMessage());
        }
    }

    /**
     * Envoie un email lors du changement de statut d'une demande
     */
    public function sendDemandeStatusChangedEmail(Demande $demande, string $oldStatus): void
    {
        // Ne pas envoyer d'email si le statut n'a pas changé
        if ($oldStatus === $demande->getStatut()) {
            return;
        }

        try {
            // Créer un seul email pour tous les destinataires
            $mail = $this->createMailer();

            // Ajouter le demandeur comme destinataire
            $mail->addAddress($demande->getDemandeur()->getEmail(), $demande->getDemandeur()->getNomComplet());

            // Si la demande a un gestionnaire différent du demandeur, l'ajouter comme destinataire
            if ($demande->getTechnicien() && $demande->getDemandeur() !== $demande->getTechnicien()) {
                $mail->addAddress($demande->getTechnicien()->getEmail(), $demande->getTechnicien()->getNomComplet());
            }

            $mail->Subject = 'Le statut d\'une demande d\'achat a changé - #' . $demande->getId();

            // Générer le contenu HTML avec Twig
            // Nous n'avons plus besoin d'ajouter un utilisateur spécifique au contexte
            // car ce mail est envoyé à plusieurs destinataires
            $htmlContent = $this->twig->render('emails/demande_status_changed.html.twig', [
                'demande' => $demande,
                'oldStatus' => $this->getStatusLabel($oldStatus),
                'newStatus' => $demande->getStatutLabel(),
                'destinataires' => 'Madame, Monsieur', // Formule générique pour plusieurs destinataires
            ]);

            $mail->isHTML(true);
            $mail->Body = $htmlContent;
            $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $htmlContent));

            $mail->send();
        } catch (Exception $e) {
            // Log l'erreur
            error_log('Erreur lors de l\'envoi de l\'email de changement de statut: ' . $e->getMessage());
        }
    }

    /**
     * Envoie un email aux administrateurs
     */
    private function sendEmailToAdmins(string $subject, string $template, array $context): void
    {
        try {
            // Récupérer tous les utilisateurs ayant des rôles avancés
            $admins = $this->userRepository->findByAdvancedRoles();

            if (empty($admins)) {
                // Si aucun admin n'est trouvé, ne rien faire
                return;
            }

            // Créer un seul email pour tous les administrateurs
            $mail = $this->createMailer();
            $mail->Subject = $subject;

            // Ajouter tous les administrateurs comme destinataires
            $hasRecipients = false;
            foreach ($admins as $admin) {
                if ($admin->getEmail()) {
                    $mail->addAddress($admin->getEmail(), $admin->getNomComplet());
                    $hasRecipients = true;
                }
            }

            // Si aucun destinataire n'a été ajouté, ne pas envoyer l'email
            if (!$hasRecipients) {
                return;
            }

            // Générer le contenu HTML avec Twig
            // Nous n'avons plus besoin d'ajouter un utilisateur spécifique au contexte
            // car ce mail est envoyé à plusieurs destinataires
            $contextWithDestinataires = array_merge($context, ['destinataires' => 'Madame, Monsieur']);
            $htmlContent = $this->twig->render($template, $contextWithDestinataires);

            $mail->isHTML(true);
            $mail->Body = $htmlContent;
            $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $htmlContent));

            $mail->send();
        } catch (Exception $e) {
            // Log l'erreur
            error_log('Erreur lors de l\'envoi de l\'email aux administrateurs: ' . $e->getMessage());
        }
    }

    /**
     * Envoie un email à des utilisateurs spécifiques (par username)
     */
    private function sendEmailToSpecificUsers(string $subject, string $template, array $context, array $usernames): void
    {
        try {
            // Récupérer les utilisateurs spécifiés par leurs usernames
            $users = $this->userRepository->findBy(['username' => $usernames]);

            if (empty($users)) {
                // Si aucun utilisateur n'est trouvé, ne rien faire
                return;
            }

            // Créer un seul email pour tous les destinataires
            $mail = $this->createMailer();
            $mail->Subject = $subject;

            // Ajouter tous les utilisateurs comme destinataires
            $hasRecipients = false;
            foreach ($users as $user) {
                if ($user->getEmail()) {
                    $mail->addAddress($user->getEmail(), $user->getNomComplet());
                    $hasRecipients = true;
                }
            }

            // Si aucun destinataire n'a été ajouté, ne pas envoyer l'email
            if (!$hasRecipients) {
                return;
            }

            // Générer le contenu HTML avec Twig
            $contextWithDestinataires = array_merge($context, ['destinataires' => 'Madame, Monsieur']);
            $htmlContent = $this->twig->render($template, $contextWithDestinataires);

            $mail->isHTML(true);
            $mail->Body = $htmlContent;
            $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $htmlContent));

            $mail->send();
        } catch (Exception $e) {
            // Log l'erreur
            error_log('Erreur lors de l\'envoi de l\'email aux utilisateurs spécifiques: ' . $e->getMessage());
        }
    }

    /**
     * Retourne le libellé d'un statut
     */
    private function getStatusLabel(string $status): string
    {
        return match($status) {
            Demande::STATUT_EN_ATTENTE => 'En attente',
            Demande::STATUT_VALIDEE => 'Validée',
            Demande::STATUT_REFUSEE => 'Refusée',
            Demande::STATUT_TRAITEE => 'Traitée',
            default => 'Inconnu'
        };
    }
}
