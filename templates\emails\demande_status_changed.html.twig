{% extends 'emails/base_email.html.twig' %}

{% block title %}Le statut de la demande d'achat a changé{% endblock %}

{% block body %}
    <h1>Le statut de la demande d'achat a changé</h1>

    <p>Bonjour {{ destinataires }},</p>

    <p>Le statut de la demande d'achat <strong>n°{{ demande.id }}</strong> a été modifié.</p>

    <div class="info-box">
        <h2>Changement de statut</h2>
        <p><strong>Ancien statut :</strong> {{ oldStatus }}</p>
        <p><strong>Nouveau statut :</strong>
            {% if newStatus == 'En attente' %}
                <span class="status status-waiting">{{ newStatus }}</span>
            {% elseif newStatus == 'Validée' %}
                <span class="status status-validated">{{ newStatus }}</span>
            {% elseif newStatus == 'Refusée' %}
                <span class="status status-refused">{{ newStatus }}</span>
            {% elseif newStatus == 'Traitée' %}
                <span class="status status-processed">{{ newStatus }}</span>
            {% else %}
                {{ newStatus }}
            {% endif %}
        </p>
    </div>

    <div class="info-box">
        <h2>Détails de la demande</h2>
        <ul>
            <li><strong>Demandeur :</strong> {{ demande.demandeur.nomComplet }}</li>
            <li><strong>Date de création :</strong> {{ demande.dateCreation|date('d/m/Y H:i') }}</li>
            <li><strong>Date souhaitée :</strong> {{ demande.dateSouhaitee|date('d/m/Y') }}</li>
            <li><strong>Urgence :</strong> {{ demande.urgenceLabel }}</li>
            <li><strong>Nature :</strong> {{ demande.natureLabel }}</li>
            <li><strong>Compte :</strong> {{ demande.compte }}</li>
            {% if demande.technicien %}
                <li><strong>Gestionnaire assigné :</strong> {{ demande.technicien.nomComplet }}</li>
            {% endif %}
            {% if demande.wbs %}
                <li><strong>WBS :</strong> {{ demande.wbs }}</li>
            {% endif %}
            {% if demande.dateGestionnaire %}
                <li><strong>Date gestionnaire :</strong> {{ demande.dateGestionnaire|date('d/m/Y') }}</li>
            {% endif %}
        </ul>
    </div>

    {% if demande.commentaireGestionnaire %}
        <div class="info-box">
            <h2>Commentaire du gestionnaire</h2>
            <p>{{ demande.commentaireGestionnaire|nl2br }}</p>
        </div>
    {% endif %}

    <a href="https://frscmanal.scmlemans.com/demandes_track/public/index.php/demande/{{ demande.id }}" class="btn">Voir la demande</a>

    <p>Cordialement,</p>

{% endblock %}
