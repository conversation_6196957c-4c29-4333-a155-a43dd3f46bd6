<?php

namespace App\Form;

use App\Entity\Demande;
use App\Entity\User;
use App\Repository\UserRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class DemandeGestionnaireType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('wbs', TextType::class, [
                'label' => 'WBS',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                    'placeholder' => 'Ex: WBS123456',
                ],
            ])
            ->add('numeroOF', TextType::class, [
                'label' => 'Numéro OF',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                    'placeholder' => 'Ex: OF123456',
                ],
            ])
            ->add('dateGestionnaire', DateType::class, [
                'widget' => 'single_text',
                'label' => 'Date de traitement',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                ],
            ])
            ->add('aSortirEnAchat', CheckboxType::class, [
                'label' => 'À sortir en Achat',
                'required' => false,
                'attr' => [
                    'class' => 'rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50',
                ],
            ])
            ->add('commentaireGestionnaire', TextareaType::class, [
                'label' => 'Commentaire du gestionnaire',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary',
                    'rows' => 3,
                    'placeholder' => 'Informations complémentaires sur le traitement de la demande...',
                ],
            ])
            ->add('items', CollectionType::class, [
                'entry_type' => DemandeItemGestionnaireType::class,
                'entry_options' => ['label' => false],
                'allow_add' => false,
                'allow_delete' => false,
                'by_reference' => false,
                'label' => false,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Demande::class,
        ]);
    }
}
