/* Custom styles */
.btn-primary {
    background-color: #3b82f6;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: white;
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.btn-secondary:hover {
    background-color: #f9fafb;
}

.btn-danger {
    background-color: #dc2626;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
}

.btn-danger:hover {
    background-color: #b91c1c;
}

.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.card {
    background-color: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
    border-radius: 0.5rem;
}

.card-header {
    padding: 1rem;
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    padding: 1rem;
    background-color: #f9fafb;
}

.badge {
    padding: 0.125rem 0.5rem;
    display: inline-flex;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 9999px;
}

.badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-danger {
    background-color: #fee2e2;
    color: #b91c1c;
}

.badge-info {
    background-color: #dbeafe;
    color: #1e40af;
}
