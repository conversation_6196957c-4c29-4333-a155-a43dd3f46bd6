<?php

namespace Proxies\__CG__\App\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class User extends \App\Entity\User implements \Doctrine\ORM\Proxy\InternalProxy
{
    use \Symfony\Component\VarExporter\LazyGhostTrait {
        initializeLazyObject as private;
        setLazyObjectAsInitialized as public __setInitialized;
        isLazyObjectInitialized as private;
        createLazyGhost as private;
        resetLazyObject as private;
    }

    public function __load(): void
    {
        $this->initializeLazyObject();
    }
    

    private const LAZY_OBJECT_PROPERTY_SCOPES = [
        "\0".parent::class."\0".'demandes' => [parent::class, 'demandes', null, 16],
        "\0".parent::class."\0".'demandesAssignees' => [parent::class, 'demandesAssignees', null, 16],
        "\0".parent::class."\0".'email' => [parent::class, 'email', null, 16],
        "\0".parent::class."\0".'id' => [parent::class, 'id', null, 16],
        "\0".parent::class."\0".'isManager' => [parent::class, 'isManager', null, 16],
        "\0".parent::class."\0".'manager' => [parent::class, 'manager', null, 16],
        "\0".parent::class."\0".'mobile' => [parent::class, 'mobile', null, 16],
        "\0".parent::class."\0".'nom' => [parent::class, 'nom', null, 16],
        "\0".parent::class."\0".'password' => [parent::class, 'password', null, 16],
        "\0".parent::class."\0".'prenom' => [parent::class, 'prenom', null, 16],
        "\0".parent::class."\0".'roles' => [parent::class, 'roles', null, 16],
        "\0".parent::class."\0".'secteur' => [parent::class, 'secteur', null, 16],
        "\0".parent::class."\0".'telephoneNumber' => [parent::class, 'telephoneNumber', null, 16],
        "\0".parent::class."\0".'titre' => [parent::class, 'titre', null, 16],
        "\0".parent::class."\0".'username' => [parent::class, 'username', null, 16],
        "\0".parent::class."\0".'vpn' => [parent::class, 'vpn', null, 16],
        'demandes' => [parent::class, 'demandes', null, 16],
        'demandesAssignees' => [parent::class, 'demandesAssignees', null, 16],
        'email' => [parent::class, 'email', null, 16],
        'id' => [parent::class, 'id', null, 16],
        'isManager' => [parent::class, 'isManager', null, 16],
        'manager' => [parent::class, 'manager', null, 16],
        'mobile' => [parent::class, 'mobile', null, 16],
        'nom' => [parent::class, 'nom', null, 16],
        'password' => [parent::class, 'password', null, 16],
        'prenom' => [parent::class, 'prenom', null, 16],
        'roles' => [parent::class, 'roles', null, 16],
        'secteur' => [parent::class, 'secteur', null, 16],
        'telephoneNumber' => [parent::class, 'telephoneNumber', null, 16],
        'titre' => [parent::class, 'titre', null, 16],
        'username' => [parent::class, 'username', null, 16],
        'vpn' => [parent::class, 'vpn', null, 16],
    ];

    public function __isInitialized(): bool
    {
        return isset($this->lazyObjectState) && $this->isLazyObjectInitialized();
    }

    public function __serialize(): array
    {
        $properties = (array) $this;
        unset($properties["\0" . self::class . "\0lazyObjectState"]);

        return $properties;
    }
}
