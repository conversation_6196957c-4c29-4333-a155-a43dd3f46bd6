<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}Gestion des demandes d'achats{% endblock %}</title>
        <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 128 128%22><text y=%221.2em%22 font-size=%2296%22>⚫️</text><text y=%221.3em%22 x=%220.2em%22 font-size=%2276%22 fill=%22%23fff%22>sf</text></svg>">
        {% block stylesheets %}
            <link rel="stylesheet" href="{{ asset('css/app.css') }}">
            <script src="https://cdn.tailwindcss.com"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
            <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
            <script>
                tailwind.config = {
                    theme: {
                        extend: {
                            colors: {
                                primary: '#3b82f6',
                                secondary: '#64748b',
                                success: '#10b981',
                                danger: '#ef4444',
                                warning: '#f59e0b',
                                info: '#3b82f6',
                            }
                        }
                    }
                }
            </script>
            <style type="text/tailwindcss">
                @layer utilities {
                    .content-auto {
                        content-visibility: auto;
                    }
                }
            </style>
            <style>
                .hidden {
                    display: none !important;
                }

                /* Styles personnalisés pour les toasts SweetAlert2 */
                .swal2-toast-custom {
                    padding: 0.75rem 1rem !important;
                    width: auto !important;
                    max-width: 350px !important;
                    font-size: 0.875rem !important;
                    font-family: inherit !important;
                }

                .swal2-toast-title-custom {
                    font-size: 0.875rem !important;
                    font-weight: 500 !important;
                    color: #333 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    line-height: 1.5 !important;
                }

                /* Ajustements pour les icônes dans les toasts */
                .swal2-toast-custom .swal2-icon {
                    margin: 0 0.5rem 0 0 !important;
                    width: 1.5rem !important;
                    height: 1.5rem !important;
                    min-width: 1.5rem !important;
                }

                .swal2-toast-custom .swal2-icon .swal2-icon-content {
                    font-size: 0.875rem !important;
                    font-weight: bold !important;
                }

                /* Styles pour les modales */
                .modal-backdrop {
                    backdrop-filter: blur(2px);
                }

                /* Animation pour les boutons */
                .btn-hover-effect {
                    transition: all 0.2s ease-in-out;
                }

                .btn-hover-effect:hover {
                    transform: translateY(-2px);
                }

                /* Styles pour les textarea */
                textarea {
                    min-height: 100px;
                    resize: vertical;
                }

                /* Amélioration de la visibilité des champs obligatoires */
                .required-field {
                    position: relative;
                }

                .required-field::after {
                    content: '*';
                    color: #ef4444;
                    position: absolute;
                    top: 0;
                    right: -10px;
                }
            </style>
        {% endblock %}

        {% block javascripts %}
            <script src="{{ asset('js/app.js') }}"></script>
            <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
            <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
            <script>
                // Configuration globale pour les toasts
                const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.addEventListener('mouseenter', Swal.stopTimer)
                        toast.addEventListener('mouseleave', Swal.resumeTimer)
                    },
                    customClass: {
                        popup: 'swal2-toast-custom',
                        title: 'swal2-toast-title-custom'
                    }
                });

                document.addEventListener('DOMContentLoaded', function() {
                    {% if app.user and app.user.isAdmin %}
                    // Fonction pour mettre à jour le compteur de demandes en attente
                    function updatePendingCount() {
                        fetch('{{ path('app_validation_box_count') }}', {
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            const countElement = document.getElementById('pending-count');
                            if (countElement) {
                                console.log('Pending count:', data.count);
                                if (data.count > 0) {
                                    countElement.textContent = data.count;
                                    countElement.classList.remove('hidden');
                                } else {
                                    countElement.classList.add('hidden');
                                }
                            } else {
                                console.error('Element #pending-count not found');
                            }
                        })
                        .catch(error => console.error('Error:', error));
                    }

                    // Mettre à jour le compteur au chargement de la page
                    updatePendingCount();

                    // Mettre à jour le compteur toutes les 60 secondes
                    setInterval(updatePendingCount, 60000);
                    {% endif %}

                    {% if app.user %}
                    // Fonction pour mettre à jour le compteur de demandes assignées
                    function updateAssignedCount() {
                        fetch('{{ path('app_assigned_requests_count') }}', {
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            const countElement = document.getElementById('assigned-count');
                            if (data.count > 0) {
                                countElement.textContent = data.count;
                                countElement.classList.remove('hidden');
                            } else {
                                countElement.classList.add('hidden');
                            }
                        })
                        .catch(error => console.error('Error:', error));
                    }

                    // Mettre à jour le compteur au chargement de la page
                    updateAssignedCount();

                    // Mettre à jour le compteur toutes les 60 secondes
                    setInterval(updateAssignedCount, 60000);
                    {% endif %}

                    // Afficher les messages flash avec Toast
                    {% for label, messages in app.flashes %}
                        {% for message in messages %}
                            Toast.fire({
                                icon: '{% if label == 'success' %}success{% elseif label == 'error' %}error{% else %}info{% endif %}',
                                title: '{{ message|e('js') }}'
                            });
                        {% endfor %}
                    {% endfor %}
                });
            </script>
        {% endblock %}
    </head>
    <body class="bg-gray-50 min-h-screen">
        <div class="min-h-screen flex flex-col">
            <header class="bg-white shadow">
                <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
                    <h1 class="text-2xl font-bold text-gray-900">
                        <a href="{{ path('app_home') }}">Commandes internes</a>
                    </h1>
                    <nav>
                        <ul class="flex space-x-4 items-center">
                            <li><a href="{{ path('app_demande_index') }}" class="text-gray-600 hover:text-gray-900">{% if app.user and app.user.isAdmin %}Demandes{% else %}Mes demandes{% endif %}</a></li>
                            <li><a href="{{ path('app_demande_new') }}" class="text-gray-600 hover:text-gray-900">Nouvelle demande</a></li>
                            {% if (app.user and app.user.isAdmin) or (app.user and app.user.demandesAssignees|length > 0) %}
                                <li>
                                    <a href="{{ path('app_assigned_requests_index') }}" class="relative inline-block text-gray-600 hover:text-gray-900">
                                        <span class="flex items-center">
                                            Demandes assignées
                                        </span>
                                        <span id="assigned-count" class="hidden absolute -top-2 -right-2 bg-blue-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">0</span>
                                    </a>
                                </li>
                            {% endif %}
                            {% if app.user and app.user.isAdmin %}
                                <li>
                                    <a href="{{ path('app_validation_box_index') }}" class="relative inline-block">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600 hover:text-gray-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                        </svg>
                                        <span id="pending-count" class="hidden absolute -top-2 -right-2 bg-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">0</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </header>
            <main class="flex-grow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <!-- Les messages flash sont maintenant gérés par Toast.fire -->

                    {% block body %}{% endblock %}
                </div>
            </main>
        </div>
    </body>
</html>
