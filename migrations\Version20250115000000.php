<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250115000000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add new fields to demande and demande_item tables';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs

        // Vérifier si les colonnes n'existent pas déjà avant de les ajouter
        $this->addSql('ALTER TABLE demande ADD gestionnaire_preferentiel_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE demande ADD a_sortir_en_achat TINYINT(1) DEFAULT NULL');
        $this->addSql('ALTER TABLE demande CHANGE compte compte VARCHAR(50) DEFAULT NULL');
        $this->addSql('ALTER TABLE demande ADD CONSTRAINT FK_2694D7A5A8B8B5F7 FOREIGN KEY (gestionnaire_preferentiel_id) REFERENCES user (id)');
        $this->addSql('CREATE INDEX IDX_2694D7A5A8B8B5F7 ON demande (gestionnaire_preferentiel_id)');

        $this->addSql('ALTER TABLE demande_item ADD date_souhaitee DATE DEFAULT NULL');
        $this->addSql('ALTER TABLE demande_item ADD lien_plan VARCHAR(500) DEFAULT NULL');
        $this->addSql('ALTER TABLE demande_item ADD date_previsionelle DATE DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE demande DROP FOREIGN KEY FK_2694D7A5A8B8B5F7');
        $this->addSql('DROP INDEX IDX_2694D7A5A8B8B5F7 ON demande');
        $this->addSql('ALTER TABLE demande DROP gestionnaire_preferentiel_id');
        $this->addSql('ALTER TABLE demande DROP a_sortir_en_achat');
        $this->addSql('ALTER TABLE demande CHANGE compte compte VARCHAR(50) NOT NULL');
        
        $this->addSql('ALTER TABLE demande_item DROP date_souhaitee');
        $this->addSql('ALTER TABLE demande_item DROP lien_plan');
        $this->addSql('ALTER TABLE demande_item DROP date_previsionelle');
    }
}
