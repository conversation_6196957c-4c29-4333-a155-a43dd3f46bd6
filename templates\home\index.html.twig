{% extends 'base.html.twig' %}

{% block title %}Tableau de bord{% endblock %}

{% block body %}
<div class="space-y-6">
    <h1 class="text-3xl font-bold text-gray-900">Tableau de bord</h1>

    {% if not app.user or not app.user.isAdmin %}
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div class="bg-white overflow-hidden shadow rounded-lg animate-fade-in-up">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">En attente</dt>
                            <dd class="text-3xl font-semibold text-gray-900"><span class="count-up">{{ stats.en_attente }}</span></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-2">
                <div class="text-sm text-gray-500">
                    {{ (stats.total > 0) ? ((stats.en_attente / stats.total * 100)|round) : 0 }}% du total
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg animate-fade-in-up animate-delay-100">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Validées</dt>
                            <dd class="text-3xl font-semibold text-gray-900"><span class="count-up">{{ stats.validees }}</span></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-2">
                <div class="text-sm text-gray-500">
                    {{ (stats.total > 0) ? ((stats.validees / stats.total * 100)|round) : 0 }}% du total
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg animate-fade-in-up animate-delay-200">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Traitées</dt>
                            <dd class="text-3xl font-semibold text-gray-900"><span class="count-up">{{ stats.traitees }}</span></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-2">
                <div class="text-sm text-gray-500">
                    {{ (stats.total > 0) ? ((stats.traitees / stats.total * 100)|round) : 0 }}% du total
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg animate-fade-in-up animate-delay-300">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Clôturées</dt>
                            <dd class="text-3xl font-semibold text-gray-900"><span class="count-up">{{ stats.cloturees }}</span></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-2">
                <div class="text-sm text-gray-500">
                    {{ (stats.total > 0) ? ((stats.cloturees / stats.total * 100)|round) : 0 }}% du total
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg animate-fade-in-up animate-delay-400">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Refusées</dt>
                            <dd class="text-3xl font-semibold text-gray-900"><span class="count-up">{{ stats.refusees }}</span></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-2 flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    {{ (stats.total > 0) ? ((stats.refusees / stats.total * 100)|round) : 0 }}% du total
                </div>
                <a href="{{ path('app_demande_index') }}" class="text-sm font-medium text-primary hover:text-blue-700">
                    Voir toutes →
                </a>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Le bouton "Voir toutes les demandes" a été supprimé -->
    {% endif %}

    {% if app.user and app.user.isAdmin %}
    <!-- Statistiques supplémentaires pour les managers -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg animate-fade-in-up">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Tableau de bord du manager</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Statistiques et informations pour les managers</p>
        </div>
        <div class="border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 p-6">
                <!-- Carte À valider -->
                <div class="bg-white border border-yellow-200 shadow-sm rounded-lg overflow-hidden animate-fade-in-up animate-delay-100 flex flex-col h-full">
                    <div class="px-4 py-5 sm:p-6 flex-grow">
                        <div class="flex items-center mb-4">
                            <div class="flex-shrink-0 bg-yellow-100 rounded-md p-3 mr-4">
                                <svg class="h-6 w-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900">À valider</h4>
                        </div>
                        <div class="flex flex-col">
                            <p class="text-3xl font-bold text-gray-900 mb-2"><span class="count-up" id="count-en-attente">{{ stats.en_attente }}</span></p>
                            <p class="text-sm text-gray-600">Demandes en attente de validation</p>
                            <div class="mt-4 text-sm text-gray-500">
                                {% if stats.total > 0 %}
                                    {{ ((stats.en_attente / stats.total) * 100)|round }}% du total des demandes
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="bg-yellow-50 px-4 py-3 border-t border-yellow-100 mt-auto">
                        <a href="{{ path('app_validation_box_index') }}" class="text-yellow-600 hover:text-yellow-800 font-medium flex items-center">
                            Voir la boîte de validation
                            <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Carte Demandes validées -->
                <div class="bg-white border border-blue-200 shadow-sm rounded-lg overflow-hidden animate-fade-in-up animate-delay-200 flex flex-col h-full">
                    <div class="px-4 py-5 sm:p-6 flex-grow">
                        <div class="flex items-center mb-4">
                            <div class="flex-shrink-0 bg-blue-100 rounded-md p-3 mr-4">
                                <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900">Validées</h4>
                        </div>
                        <div class="flex flex-col">
                            <p class="text-3xl font-bold text-gray-900 mb-2"><span class="count-up">{{ stats.validees }}</span></p>
                            <p class="text-sm text-gray-600">Demandes validées en attente de traitement</p>
                            <div class="mt-4 text-sm text-gray-500">
                                {% if stats.validees > 0 %}
                                    {{ ((stats.validees / (stats.validees + stats.traitees + stats.cloturees)) * 100)|round }}% des demandes validées
                                {% else %}
                                    Aucune demande en attente
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="bg-blue-50 px-4 py-3 border-t border-blue-100 mt-auto">
                        <a href="{{ path('app_assigned_requests_index') }}" class="text-blue-600 hover:text-blue-800 font-medium flex items-center">
                            Voir les demandes assignées
                            <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Carte Demandes traitées -->
                <div class="bg-white border border-green-200 shadow-sm rounded-lg overflow-hidden animate-fade-in-up animate-delay-300 flex flex-col h-full">
                    <div class="px-4 py-5 sm:p-6 flex-grow">
                        <div class="flex items-center mb-4">
                            <div class="flex-shrink-0 bg-green-100 rounded-md p-3 mr-4">
                                <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900">Traitées</h4>
                        </div>
                        <div class="flex flex-col">
                            <p class="text-3xl font-bold text-gray-900 mb-2"><span class="count-up">{{ stats.traitees }}</span></p>
                            <p class="text-sm text-gray-600">Demandes complétées par les gestionnaires</p>
                            <div class="mt-4 text-sm text-gray-500">
                                {% if stats.total > 0 %}
                                    {{ ((stats.traitees / stats.total) * 100)|round }}% du total des demandes
                                {% else %}
                                    Aucune demande traitée
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="bg-green-50 px-4 py-3 border-t border-green-100 mt-auto">
                        <a href="{{ path('app_assigned_requests_index') }}" class="text-green-600 hover:text-green-800 font-medium flex items-center">
                            Voir les demandes traitées
                            <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Carte Demandes clôturées -->
                <div class="bg-white border border-purple-200 shadow-sm rounded-lg overflow-hidden animate-fade-in-up animate-delay-400 flex flex-col h-full">
                    <div class="px-4 py-5 sm:p-6 flex-grow">
                        <div class="flex items-center mb-4">
                            <div class="flex-shrink-0 bg-purple-100 rounded-md p-3 mr-4">
                                <svg class="h-6 w-6 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900">Clôturées</h4>
                        </div>
                        <div class="flex flex-col">
                            <p class="text-3xl font-bold text-gray-900 mb-2"><span class="count-up">{{ stats.cloturees }}</span></p>
                            <p class="text-sm text-gray-600">Demandes complètement finalisées</p>
                            <div class="mt-4 text-sm text-gray-500">
                                {% if stats.total > 0 %}
                                    {{ ((stats.cloturees / stats.total) * 100)|round }}% du total des demandes
                                {% else %}
                                    Aucune demande clôturée
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="bg-purple-50 px-4 py-3 border-t border-purple-100 mt-auto">
                        <a href="{{ path('app_assigned_requests_history') }}" class="text-purple-600 hover:text-purple-800 font-medium flex items-center">
                            Voir les demandes clôturées
                            <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Graphique de répartition des demandes -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg animate-fade-in-up">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Répartition des demandes</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                {% if app.user and app.user.isAdmin %}
                    Vue d'ensemble de l'état de toutes les demandes
                {% else %}
                    Vue d'ensemble de l'état de vos demandes
                {% endif %}
            </p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div class="w-full bg-gray-200 rounded-full h-4 mb-6">
                {% set en_attente_percent = (stats.total > 0) ? (stats.en_attente / stats.total * 100) : 0 %}
                {% set validees_percent = (stats.total > 0) ? (stats.validees / stats.total * 100) : 0 %}
                {% set traitees_percent = (stats.total > 0) ? (stats.traitees / stats.total * 100) : 0 %}
                {% set cloturees_percent = (stats.total > 0) ? (stats.cloturees / stats.total * 100) : 0 %}
                {% set refusees_percent = (stats.total > 0) ? (stats.refusees / stats.total * 100) : 0 %}

                <div class="flex h-4 rounded-full overflow-hidden" id="progress-bar-container">
                    {% if en_attente_percent > 0 %}
                        <div class="bg-yellow-100 h-4 progress-bar-segment" data-status="en_attente" data-percent="{{ en_attente_percent }}" style="width: {{ en_attente_percent }}%; border-right: 1px solid white;"></div>
                    {% endif %}
                    {% if validees_percent > 0 %}
                        <div class="bg-blue-100 h-4 progress-bar-segment" data-status="validees" data-percent="{{ validees_percent }}" style="width: {{ validees_percent }}%; border-right: 1px solid white;"></div>
                    {% endif %}
                    {% if traitees_percent > 0 %}
                        <div class="bg-green-100 h-4 progress-bar-segment" data-status="traitees" data-percent="{{ traitees_percent }}" style="width: {{ traitees_percent }}%; border-right: 1px solid white;"></div>
                    {% endif %}
                    {% if cloturees_percent > 0 %}
                        <div class="bg-purple-100 h-4 progress-bar-segment" data-status="cloturees" data-percent="{{ cloturees_percent }}" style="width: {{ cloturees_percent }}%; border-right: 1px solid white;"></div>
                    {% endif %}
                    {% if refusees_percent > 0 %}
                        <div class="bg-red-100 h-4 progress-bar-segment" data-status="refusees" data-percent="{{ refusees_percent }}" style="width: {{ refusees_percent }}%; border-right: 1px solid white;"></div>
                    {% endif %}
                </div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-yellow-100 border border-yellow-600 rounded-full mr-2"></div>
                    <span class="text-sm text-yellow-600">En attente ({{ en_attente_percent|round }}%)</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-blue-100 border border-blue-600 rounded-full mr-2"></div>
                    <span class="text-sm text-blue-600">Validées ({{ validees_percent|round }}%)</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-green-100 border border-green-600 rounded-full mr-2"></div>
                    <span class="text-sm text-green-600">Traitées ({{ traitees_percent|round }}%)</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-purple-100 border border-purple-600 rounded-full mr-2"></div>
                    <span class="text-sm text-purple-600">Clôturées ({{ cloturees_percent|round }}%)</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-red-100 border border-red-600 rounded-full mr-2"></div>
                    <span class="text-sm text-red-600">Refusées ({{ refusees_percent|round }}%)</span>
                </div>
            </div>
        </div>
    </div>



    <div class="bg-white shadow overflow-hidden sm:rounded-lg animate-fade-in-up">
        <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Demandes récentes</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">{% if app.user and app.user.isAdmin %}Les 5 dernières demandes{% else %}Vos 5 dernières demandes{% endif %}</p>
            </div>
            <a href="{{ path('app_demande_new') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Nouvelle demande
            </a>
        </div>
        <div class="border-t border-gray-200">
            {% if recent_demandes|length > 0 %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nature</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Urgence</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for demande in recent_demandes %}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ demande.dateCreation|date('d/m/Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ demande.natureLabel }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            {% if demande.urgence == 'haute' %}
                                                bg-red-100 text-red-800
                                            {% elseif demande.urgence == 'moyenne' %}
                                                bg-yellow-100 text-yellow-800
                                            {% else %}
                                                bg-green-100 text-green-800
                                            {% endif %}
                                        ">
                                            {{ demande.urgenceLabel }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            {% if demande.statut == 'en_attente' %}
                                                bg-yellow-100 text-yellow-800
                                            {% elseif demande.statut == 'validee' %}
                                                bg-blue-100 text-blue-800
                                            {% elseif demande.statut == 'refusee' %}
                                                bg-red-100 text-red-800
                                            {% elseif demande.statut == 'traitee' %}
                                                bg-green-100 text-green-800
                                            {% elseif demande.statut == 'cloturee' %}
                                                bg-purple-100 text-purple-800
                                            {% else %}
                                                bg-gray-100 text-gray-800
                                            {% endif %}
                                        ">
                                            {{ demande.statutLabel }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="{{ path('app_demande_show', {'id': demande.id}) }}" class="inline-flex items-center p-1.5 text-sm text-gray-600 hover:text-primary transition-colors duration-200" title="Voir les détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if demande.statut not in ['traitee', 'cloturee', 'refusee'] %}
                                                <a href="{{ path('app_demande_edit', {'id': demande.id}) }}" class="inline-flex items-center p-1.5 text-sm text-gray-600 hover:text-green-600 transition-colors duration-200" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="px-6 py-4 text-center text-gray-500">
                    Aucune demande récente. <a href="{{ path('app_demande_new') }}" class="text-primary hover:underline">Créer une nouvelle demande</a>.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Animation pour les barres de progression */
        @keyframes fadeInProgressBar {
            from { opacity: 0.3; }
            to { opacity: 1; }
        }

        .progress-bar-animated .progress-bar-segment {
            animation: fadeInProgressBar 1.5s ease-out forwards;
        }

        /* Animation pour les cartes statistiques */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            opacity: 0;
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .animate-delay-100 { animation-delay: 0.1s; }
        .animate-delay-200 { animation-delay: 0.2s; }
        .animate-delay-300 { animation-delay: 0.3s; }
        .animate-delay-400 { animation-delay: 0.4s; }
        .animate-delay-500 { animation-delay: 0.5s; }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/countup.js/2.0.7/countUp.umd.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Synchroniser avec le compteur de la barre de navigation
            {% if app.user and app.user.isAdmin %}
            // Récupérer le nombre de demandes en attente
            fetch('{{ path('app_validation_box_count') }}', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('Fetched pending count for dashboard:', data.count);
                // Mettre à jour le compteur dans le tableau de bord
                const countEnAttente = document.getElementById('count-en-attente');
                if (countEnAttente) {
                    // Mettre à jour directement le texte pour s'assurer que la valeur est correcte
                    countEnAttente.textContent = data.count;

                    // Mettre à jour également le pourcentage si nécessaire
                    const totalDemandes = {{ stats.total }};
                    if (totalDemandes > 0) {
                        // Mettre à jour le pourcentage dans la carte
                        const percentElement = countEnAttente.closest('.flex-col').querySelector('.mt-4.text-sm.text-gray-500');
                        if (percentElement) {
                            const percent = Math.round((data.count / totalDemandes) * 100);
                            percentElement.textContent = percent + '% du total des demandes';
                        }

                        // Mettre à jour le pourcentage dans la légende de la barre de progression
                        const legendElement = document.querySelector('.text-sm.text-yellow-600');
                        if (legendElement) {
                            legendElement.textContent = 'En attente (' + percent + '%)';
                        }

                        // Mettre à jour la barre de progression
                        const progressSegment = document.querySelector('.progress-bar-segment[data-status="en_attente"]');
                        if (progressSegment) {
                            progressSegment.style.width = percent + '%';
                            progressSegment.setAttribute('data-percent', percent);
                        }
                    }
                }
            })
            .catch(error => console.error('Error:', error));
            {% endif %}

            // Animation des compteurs
            const countElements = document.querySelectorAll('.count-up');
            countElements.forEach(function(element) {
                // Stocker la valeur originale
                const originalValue = element.textContent;
                const value = parseInt(originalValue);

                // Ne pas réinitialiser à 0 pour éviter le clignotement si l'animation échoue

                try {
                    // Utiliser la version UMD de CountUp
                    if (typeof countUp !== 'undefined' && typeof countUp.CountUp === 'function') {
                        // Version UMD avec namespace countUp
                        const counter = new countUp.CountUp(element, value, {
                            duration: 2,
                            useEasing: true,
                            useGrouping: true,
                        });

                        // Vérifier si l'élément est visible dans la fenêtre
                        const observer = new IntersectionObserver((entries) => {
                            entries.forEach(entry => {
                                if (entry.isIntersecting) {
                                    counter.start();
                                    observer.unobserve(entry.target);
                                }
                            });
                        });

                        observer.observe(element);
                    } else {
                        // Fallback: afficher directement la valeur
                        console.warn("La bibliothèque CountUp n'est pas correctement chargée");
                        element.textContent = originalValue;
                    }
                } catch (e) {
                    console.error('Erreur lors de l\'initialisation de CountUp:', e);
                    element.textContent = originalValue;
                }
            });

            // Animation de la barre de progression complète plutôt que des segments individuels
            const progressBarContainer = document.getElementById('progress-bar-container');
            if (progressBarContainer) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Récupérer tous les segments
                            const segments = progressBarContainer.querySelectorAll('.progress-bar-segment');

                            // Vérifier s'il y a des segments
                            if (segments.length > 0) {
                                // Ajouter une classe d'animation au conteneur
                                progressBarContainer.classList.add('progress-bar-animated');

                                // S'assurer que les largeurs sont correctes après l'animation
                                segments.forEach(segment => {
                                    const percent = segment.getAttribute('data-percent');
                                    if (percent) {
                                        // Appliquer la largeur correcte
                                        segment.style.width = percent + '%';
                                    }
                                });
                            }

                            observer.unobserve(entry.target);
                        }
                    });
                });

                observer.observe(progressBarContainer);
            }
        });
    </script>
{% endblock %}