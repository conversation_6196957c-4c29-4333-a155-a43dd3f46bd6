{% extends 'base.html.twig' %}

{% block title %}Liste des demandes{% endblock %}

{% block body %}
<div class="space-y-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Liste des demandes</h1>
            <p class="mt-1 text-sm text-gray-600">Consultez et filtrez toutes les demandes du système</p>
        </div>
        <div class="flex space-x-3">
            {% if is_granted('ROLE_ADMIN') %}
                <a href="{{ path('app_export_demandes_csv') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary">
                    <i class="fas fa-file-excel mr-2 text-green-600"></i>
                    Exporter en CSV
                </a>
            {% endif %}
            <a href="{{ path('app_demande_new') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary">
                <i class="fas fa-plus mr-2"></i>
                Nouvelle demande
            </a>
        </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg border border-gray-200">
        <div class="px-6 py-6 sm:p-6 bg-gray-50">
            <div id="filter-form" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div class="filter-group">
                    <label for="statut" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                    <div class="relative">
                        <select id="statut" class="appearance-none block w-full px-3 py-2 text-base border-gray-300 bg-white focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                            <option value="">Tous les statuts</option>
                            <option value="en_attente">En attente</option>
                            <option value="validee">Validée</option>
                            <option value="refusee">Refusée</option>
                            <option value="traitee">Traitée</option>
                            <option value="cloturee">Clôturée</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                        </div>
                    </div>
                </div>

                <div class="filter-group">
                    <label for="urgence" class="block text-sm font-medium text-gray-700 mb-1">Urgence</label>
                    <div class="relative">
                        <select id="urgence" class="appearance-none block w-full px-3 py-2 text-base border-gray-300 bg-white focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                            <option value="">Toutes les urgences</option>
                            <option value="basse">Basse</option>
                            <option value="moyenne">Moyenne</option>
                            <option value="haute">Haute</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                        </div>
                    </div>
                </div>

                <div class="filter-group">
                    <label for="nature" class="block text-sm font-medium text-gray-700 mb-1">Nature</label>
                    <div class="relative">
                        <select id="nature" class="appearance-none block w-full px-3 py-2 text-base border-gray-300 bg-white focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                            <option value="">Toutes les natures</option>
                            <option value="echantillons">Échantillons</option>
                            <option value="outillage">Outillage</option>
                            <option value="pieces">Pièces</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                        </div>
                    </div>
                </div>

                <div class="filter-group">
                    <label for="technicien" class="block text-sm font-medium text-gray-700 mb-1">Gestionnaire</label>
                    <div class="relative">
                        <select id="technicien" class="appearance-none block w-full px-3 py-2 text-base border-gray-300 bg-white focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                            <option value="">Tous les gestionnaires</option>
                            {% for technicien in techniciens %}
                                <option value="{{ technicien.id }}">{{ technicien.nomComplet }}</option>
                            {% endfor %}
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                        </div>
                    </div>
                </div>

                <div class="filter-group">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400 text-xs"></i>
                        </div>
                        <input type="text" id="search" class="pl-8 pr-3 py-2 block w-full rounded-md border-gray-300 focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Référence, personne concernée, gestionnaire...">
                    </div>
                </div>

                <div class="flex items-end">
                    <button type="button" id="reset-filters" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary">
                        <i class="fas fa-times mr-2"></i>
                        Réinitialiser
                    </button>
                </div>
            </div>
        </div>

        <div id="demandes-list">
            {% include 'demande/_list.html.twig' with {'demandes': demandes} %}
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Styles pour SweetAlert2 */
        .swal2-confirm-button {
            font-weight: 500 !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: 0.5rem !important;
            transition: all 0.2s ease-in-out !important;
            box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.2), 0 2px 4px -1px rgba(239, 68, 68, 0.1) !important;
        }
        .swal2-confirm-button:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.2), 0 4px 6px -2px rgba(239, 68, 68, 0.1) !important;
        }
        .swal2-cancel-button {
            font-weight: 500 !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: 0.5rem !important;
            transition: all 0.2s ease-in-out !important;
            box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.1), 0 2px 4px -1px rgba(107, 114, 128, 0.06) !important;
        }
        .swal2-cancel-button:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 10px 15px -3px rgba(107, 114, 128, 0.1), 0 4px 6px -2px rgba(107, 114, 128, 0.06) !important;
        }
        .swal2-popup {
            border-radius: 0.75rem !important;
            padding: 2rem !important;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
        }
        .swal2-title {
            font-size: 1.5rem !important;
            margin-bottom: 1rem !important;
        }
        .swal2-html-container {
            margin-bottom: 1.5rem !important;
        }

        /* Animations pour les filtres */
        .filter-group {
            transition: all 0.3s ease;
        }
        .filter-group:hover {
            transform: translateY(-2px);
        }

        /* Styles pour le tableau */
        #demandes-table {
            border-collapse: separate;
            border-spacing: 0;
        }

        #demandes-table th {
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        #demandes-table tr {
            transition: all 0.2s ease;
        }

        #demandes-table tr:hover {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            z-index: 1;
        }

        /* Animation pour les badges */
        #demandes-table span.rounded-full {
            transition: all 0.2s ease;
        }

        #demandes-table span.rounded-full:hover {
            transform: scale(1.05);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* Animation pour les boutons d'action */
        #demandes-table a {
            transition: all 0.2s ease;
        }

        #demandes-table a:hover {
            transform: scale(1.15);
        }

        /* Effet de pulsation pour le bouton de nouvelle demande */
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
            }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Fonction pour confirmer la suppression d'une demande
        function confirmDelete(id, event) {
            event.preventDefault();

            Swal.fire({
                title: '<span class="text-red-600">Êtes-vous sûr ?</span>',
                html: '<div class="text-gray-700 mb-4">Cette action est <strong>irréversible</strong>.<br>La demande #' + id + ' sera définitivement supprimée.</div>',
                icon: 'warning',
                iconColor: '#ef4444',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: '<i class="fas fa-trash-alt mr-2"></i>Oui, supprimer',
                cancelButtonText: '<i class="fas fa-times mr-2"></i>Annuler',
                customClass: {
                    confirmButton: 'swal2-confirm-button',
                    cancelButton: 'swal2-cancel-button',
                    title: 'text-lg font-bold',
                    popup: 'rounded-lg shadow-lg border border-gray-200'
                },
                buttonsStyling: true,
                padding: '2em',
                showClass: {
                    popup: 'animate__animated animate__fadeInDown animate__faster'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutUp animate__faster'
                },
                backdrop: `rgba(0,0,0,0.4)`,
                allowOutsideClick: false,
                allowEscapeKey: true,
                allowEnterKey: false,
                focusConfirm: false,
                focusCancel: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Afficher une animation de succès avant de soumettre le formulaire
                    Swal.fire({
                        title: 'Suppression en cours...',
                        html: 'La demande est en cours de suppression.',
                        timer: 1500,
                        timerProgressBar: true,
                        didOpen: () => {
                            Swal.showLoading();
                        },
                        willClose: () => {
                            // Créer un formulaire de suppression et le soumettre
                            const form = document.createElement('form');
                            form.method = 'POST';
                            form.action = '{{ path('app_demande_index') }}/' + id;

                            // Ajouter le token CSRF
                            const csrfToken = document.createElement('input');
                            csrfToken.type = 'hidden';
                            csrfToken.name = '_token';
                            csrfToken.value = '{{ csrf_token('delete') }}' + id;
                            form.appendChild(csrfToken);

                            document.body.appendChild(form);
                            form.submit();
                        }
                    });
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Fonction pour filtrer les demandes directement dans le DOM
            function applyFilters() {
                // Récupérer les valeurs des filtres directement depuis les éléments
                const statutFilter = document.getElementById('statut').value;
                const urgenceFilter = document.getElementById('urgence').value;
                const natureFilter = document.getElementById('nature').value;
                const technicienFilter = document.getElementById('technicien').value;
                const searchFilter = document.getElementById('search').value.toLowerCase();

                // Récupérer toutes les lignes de demandes
                const demandeRows = document.querySelectorAll('#demandes-table tbody tr');
                let visibleCount = 0;

                // Parcourir chaque ligne et appliquer les filtres
                demandeRows.forEach(row => {
                    // Récupérer les valeurs des attributs data-*
                    const statut = row.getAttribute('data-statut');
                    const urgence = row.getAttribute('data-urgence');
                    const nature = row.getAttribute('data-nature');
                    const technicienId = row.getAttribute('data-technicien-id');
                    const searchableContent = row.getAttribute('data-searchable').toLowerCase();

                    // Appliquer les filtres
                    let visible = true;

                    // Filtre par statut
                    if (statutFilter && statut !== statutFilter) {
                        visible = false;
                    }

                    // Filtre par urgence
                    if (visible && urgenceFilter && urgence !== urgenceFilter) {
                        visible = false;
                    }

                    // Filtre par nature
                    if (visible && natureFilter && nature !== natureFilter) {
                        visible = false;
                    }

                    // Filtre par gestionnaire
                    if (visible && technicienFilter && technicienId !== technicienFilter) {
                        visible = false;
                    }

                    // Filtre par recherche
                    if (visible && searchFilter && !searchableContent.includes(searchFilter)) {
                        visible = false;
                    }

                    // Afficher ou masquer la ligne
                    if (visible) {
                        row.classList.remove('hidden');
                        visibleCount++;
                    } else {
                        row.classList.add('hidden');
                    }
                });

                // Afficher un message si aucune demande ne correspond aux filtres
                const noResultsMessage = document.getElementById('no-results-message');
                const tableContainer = document.getElementById('table-container');

                if (visibleCount === 0) {
                    noResultsMessage.classList.remove('hidden');
                    tableContainer.classList.add('hidden');
                } else {
                    noResultsMessage.classList.add('hidden');
                    tableContainer.classList.remove('hidden');
                }
            }

            // Ajouter les écouteurs d'événements pour les filtres
            const statut = document.getElementById('statut');
            const urgence = document.getElementById('urgence');
            const nature = document.getElementById('nature');
            const technicien = document.getElementById('technicien');
            const search = document.getElementById('search');
            const resetFilters = document.getElementById('reset-filters');

            statut.addEventListener('change', applyFilters);
            urgence.addEventListener('change', applyFilters);
            nature.addEventListener('change', applyFilters);
            technicien.addEventListener('change', applyFilters);

            // Ajouter un délai pour la recherche en temps réel
            let timeout = null;
            search.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(applyFilters, 300);
            });

            // Empêcher la soumission du formulaire lorsque l'utilisateur appuie sur Entrée dans le champ de recherche
            search.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    applyFilters();
                }
            });

            // Gérer le bouton de réinitialisation des filtres
            resetFilters.addEventListener('click', function() {
                // Réinitialiser tous les champs du formulaire
                statut.value = '';
                urgence.value = '';
                nature.value = '';
                technicien.value = '';
                search.value = '';

                // Appliquer les filtres (qui montrera toutes les demandes)
                applyFilters();

                // Animation de confirmation
                resetFilters.classList.add('bg-green-100');
                resetFilters.innerHTML = '<i class="fas fa-check mr-2"></i> Filtres réinitialisés';

                setTimeout(() => {
                    resetFilters.classList.remove('bg-green-100');
                    resetFilters.innerHTML = '<i class="fas fa-times mr-2"></i> Réinitialiser';
                }, 1500);
            });

            // Ajouter un écouteur d'événement pour le lien de réinitialisation des filtres dans le message "Aucun résultat"
            const resetFiltersLink = document.getElementById('reset-filters-link');
            if (resetFiltersLink) {
                resetFiltersLink.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Réinitialiser tous les champs du formulaire
                    statut.value = '';
                    urgence.value = '';
                    nature.value = '';
                    technicien.value = '';
                    search.value = '';

                    // Appliquer les filtres
                    applyFilters();

                    // Animation de confirmation sur le bouton principal de réinitialisation
                    resetFilters.classList.add('bg-green-100');
                    resetFilters.innerHTML = '<i class="fas fa-check mr-2"></i> Filtres réinitialisés';

                    setTimeout(() => {
                        resetFilters.classList.remove('bg-green-100');
                        resetFilters.innerHTML = '<i class="fas fa-times mr-2"></i> Réinitialiser';
                    }, 1500);
                });
            }

            // Ne pas utiliser les filtres de l'URL pour éviter les problèmes avec _list.html.twig

            // Appliquer les filtres au chargement
            applyFilters();
        });
    </script>
{% endblock %}
