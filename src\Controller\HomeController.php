<?php

namespace App\Controller;

use App\Repository\DemandeRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class HomeController extends AbstractController
{
    #[Route('/', name: 'app_home')]
    public function index(DemandeRepository $demandeRepository): Response
    {
        $user = $this->getUser();

        // Récupérer les statistiques
        if ($user && $user->isAdmin()) {
            // Pour les administrateurs, afficher toutes les demandes
            $demandes = $demandeRepository->findBy([], ['dateCreation' => 'DESC']);
        } else {
            // Pour les utilisateurs normaux, afficher uniquement leurs demandes
            $demandes = $demandeRepository->findByFilters([], $user);
        }

        $stats = [
            'total' => count($demandes),
            'en_attente' => 0,
            'validees' => 0,
            'refusees' => 0,
            'traitees' => 0,
            'cloturees' => 0,
        ];

        foreach ($demandes as $demande) {
            switch ($demande->getStatut()) {
                case 'en_attente':
                    $stats['en_attente']++;
                    break;
                case 'validee':
                    $stats['validees']++;
                    break;
                case 'refusee':
                    $stats['refusees']++;
                    break;
                case 'traitee':
                    $stats['traitees']++;
                    break;
                case 'cloturee':
                    $stats['cloturees']++;
                    break;
            }
        }

        // Récupérer les 5 dernières demandes
        $recentDemandes = array_slice($demandes, 0, 5);

        return $this->render('home/index.html.twig', [
            'stats' => $stats,
            'recent_demandes' => $recentDemandes,
        ]);
    }
}
