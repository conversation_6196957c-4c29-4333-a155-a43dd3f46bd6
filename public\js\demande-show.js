// JavaScript for the demande show page

document.addEventListener('DOMContentLoaded', function() {
    // Initialize delete button functionality
    initializeDeleteButton();

    // Initialize form submissions with AJAX
    initializeFormSubmissions();

    // Initialize modal functionality
    initializeModals();
});

// Delete button functionality
function initializeDeleteButton() {
    const deleteButton = document.getElementById('delete-button');
    if (!deleteButton) return;

    deleteButton.addEventListener('click', function(e) {
        e.preventDefault();

        // Use SweetAlert2 for confirmation
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: "Cette action ne peut pas être annulée !",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#64748b',
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-secondary'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Submit the delete form
                document.getElementById('delete-form').submit();
            }
        });
    });
}

// Form submissions with AJAX
function initializeFormSubmissions() {
    // Gestionnaire form
    const gestionnaireForm = document.getElementById('gestionnaire-update-form');
    if (gestionnaireForm) {
        gestionnaireForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Assurons-nous que le jeton CSRF est à jour
            const csrfToken = gestionnaireForm.querySelector('input[name="_token"]');
            if (csrfToken && window.CSRF_TOKENS && window.CSRF_TOKENS.update_gestionnaire) {
                csrfToken.value = window.CSRF_TOKENS.update_gestionnaire;
            }

            submitFormWithAjax(gestionnaireForm, 'gestionnaire-update-status', updateGestionnaireFields);
        });
    }

    // Admin form
    const adminForm = document.getElementById('admin-update-form');
    if (adminForm) {
        adminForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Assurons-nous que le jeton CSRF est à jour
            const csrfToken = adminForm.querySelector('input[name="_token"]');
            if (csrfToken && window.CSRF_TOKENS && window.CSRF_TOKENS.update_admin) {
                csrfToken.value = window.CSRF_TOKENS.update_admin;
            }

            submitFormWithAjax(adminForm, 'admin-update-status', updateAdminFields);
        });
    }
}

// Submit form with AJAX
function submitFormWithAjax(form, statusElementId, successCallback) {
    const formData = new FormData(form);

    // Add CSRF token if needed
    const csrfToken = form.querySelector('input[name="_token"]');
    if (csrfToken) {
        formData.append('_token', csrfToken.value);
        console.log('Using CSRF token:', csrfToken.value);
    } else {
        console.warn('No CSRF token found in form');
    }

    // Log form data for debugging
    console.log('Form action:', form.action);
    console.log('Form method:', form.method);
    console.log('Form ID:', form.id);

    // Log form data entries
    for (let [key, value] of formData.entries()) {
        console.log(`Form data: ${key} = ${value}`);
    }

    // Add X-Requested-With header for Symfony to detect AJAX request
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            const statusElement = document.getElementById(statusElementId);
            if (statusElement) {
                statusElement.classList.remove('hidden');
                setTimeout(() => {
                    statusElement.classList.add('hidden');
                }, 3000);
            }

            // Show toast notification
            Toast.fire({
                icon: 'success',
                title: data.message || 'Mise à jour effectuée'
            });

            // Update fields
            if (successCallback) {
                successCallback(data);
            }
        } else {
            // Show error message
            Toast.fire({
                icon: 'error',
                title: data.message || 'Une erreur est survenue'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Toast.fire({
            icon: 'error',
            title: 'Une erreur est survenue lors de la communication avec le serveur'
        });
    });
}

// Update fields after gestionnaire form submission
function updateGestionnaireFields(data) {
    // Update WBS
    if (data.wbs) {
        const wbsElement = document.getElementById('wbs-value');
        if (wbsElement) {
            wbsElement.textContent = data.wbs;
        }
    }

    // Update date gestionnaire
    if (data.dateGestionnaire) {
        const dateElement = document.getElementById('date-gestionnaire-value');
        if (dateElement) {
            dateElement.textContent = data.dateGestionnaire;
        }
    }

    // Update commentaire gestionnaire
    if (data.commentaire) {
        // Mettre à jour l'affichage du commentaire dans la section d'information
        const commentaireElement = document.getElementById('commentaire-gestionnaire');
        if (commentaireElement) {
            commentaireElement.innerHTML = data.commentaire;
        }

        // Mettre à jour le champ textarea du formulaire gestionnaire
        const commentaireInput = document.getElementById('gestionnaire-commentaire');
        if (commentaireInput) {
            commentaireInput.value = data.commentaire;
        }
    }

    // Ne pas vider le formulaire car nous affichons maintenant le commentaire complet
    // document.getElementById('gestionnaire-commentaire').value = '';
}

// Update fields after admin form submission
function updateAdminFields(data) {
    console.log('Updating admin fields with data:', data);

    // Update status badge
    if (data.statut) {
        const statusBadge = document.getElementById('status-badge');
        if (statusBadge) {
            console.log('Status badge found, updating to:', data.statut);

            // Remove all existing color classes
            statusBadge.classList.remove(
                'bg-yellow-100', 'text-yellow-800',
                'bg-blue-100', 'text-blue-800',
                'bg-red-100', 'text-red-800',
                'bg-green-100', 'text-green-800',
                'bg-purple-100', 'text-purple-800',
                'bg-gray-100', 'text-gray-800'
            );

            // Add appropriate color classes based on new status
            if (data.newStatus === 'en_attente') {
                statusBadge.classList.add('bg-yellow-100', 'text-yellow-800');
            } else if (data.newStatus === 'validee') {
                statusBadge.classList.add('bg-blue-100', 'text-blue-800');
            } else if (data.newStatus === 'refusee') {
                statusBadge.classList.add('bg-red-100', 'text-red-800');
            } else if (data.newStatus === 'traitee') {
                statusBadge.classList.add('bg-green-100', 'text-green-800');
            } else if (data.newStatus === 'cloturee') {
                statusBadge.classList.add('bg-purple-100', 'text-purple-800');
            }

            statusBadge.textContent = data.statut;
        } else {
            console.warn('Status badge not found');
        }
    } else {
        console.warn('No status data received');
    }

    // Update WBS
    if (data.wbs) {
        const wbsElement = document.getElementById('wbs-value');
        if (wbsElement) {
            wbsElement.textContent = data.wbs;
        }
    }

    // Update date gestionnaire
    if (data.dateGestionnaire) {
        const dateElement = document.getElementById('date-gestionnaire-value');
        if (dateElement) {
            dateElement.textContent = data.dateGestionnaire;
        }
    }

    // Update commentaire gestionnaire
    if (data.commentaire) {
        // Mettre à jour l'affichage du commentaire dans la section d'information
        const commentaireElement = document.getElementById('commentaire-gestionnaire');
        if (commentaireElement) {
            commentaireElement.innerHTML = data.commentaire;
        }

        // Mettre à jour le champ textarea du formulaire admin
        const commentaireInput = document.getElementById('commentaire');
        if (commentaireInput) {
            commentaireInput.value = data.commentaire;
        }
    }

    // Ne pas vider le formulaire car nous affichons maintenant le commentaire complet
    // document.getElementById('commentaire').value = '';

    // If status changed, update the timeline
    if (data.statusChanged) {
        // Reload the page to update the timeline and action buttons
        window.location.reload();
    }
}

// Modal functionality
function initializeModals() {
    // These functions are called from onclick attributes in the HTML
    window.openValidateModal = function(id) {
        Swal.fire({
            title: 'Valider la demande',
            html: `
                <form id="validate-form" class="text-left">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mr-4">
                            <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2">
                                Valider la demande
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Êtes-vous sûr de vouloir valider cette demande ? Veuillez assigner un gestionnaire et ajouter un commentaire si nécessaire.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="bg-blue-50 p-4 rounded-md border border-blue-100">
                            <label for="technicien" class="block text-sm font-medium text-blue-800 mb-2">Gestionnaire assigné <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user-cog text-gray-400"></i>
                                </div>
                                <select id="technicien" name="technicien" class="pl-10 block w-full py-3 px-4 border border-blue-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base" required>
                                    <option value="">Sélectionnez un gestionnaire</option>
                                    <!-- Les options seront chargées via AJAX -->
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="validate-commentaire" class="block text-sm font-medium text-gray-700 mb-2">Commentaire (optionnel)</label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment"></i>
                                </div>
                                <textarea id="validate-commentaire" name="commentaire" rows="4" class="pl-10 block w-full py-3 px-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base" placeholder="Ajoutez un commentaire si nécessaire..."></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            `,
            showCancelButton: true,
            confirmButtonText: 'Valider',
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#3b82f6',
            cancelButtonColor: '#64748b',
            customClass: {
                confirmButton: 'btn-hover-effect',
                cancelButton: 'btn-hover-effect'
            },
            focusConfirm: false,
            didOpen: () => {
                fetch('/demandes_track/public/index.php/user/technicians', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    const select = document.getElementById('technicien');
                    data.forEach(tech => {
                        const option = document.createElement('option');
                        option.value = tech.id;
                        option.textContent = tech.nomComplet;
                        select.appendChild(option);
                    });
                })
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const technicien = document.getElementById('technicien').value;
                const commentaire = document.getElementById('validate-commentaire').value;

                if (!technicien) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Veuillez sélectionner un gestionnaire'
                    });
                    return;
                }

                // Submit validation form
                const formData = new FormData();
                formData.append('technicien', technicien);
                formData.append('commentaire', commentaire);
                formData.append('_token', window.CSRF_TOKENS.validate_demande);

                fetch(`/demandes_track/public/index.php/validation-box/validate/${id}`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        Toast.fire({
                            icon: 'success',
                            title: 'Demande validée'
                        });
                        // Reload page after successful validation
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        throw new Error('Network response was not ok');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toast.fire({
                        icon: 'error',
                        title: 'Une erreur est survenue'
                    });
                });
            }
        });
    };

    window.openRejectModal = function(id) {
        Swal.fire({
            title: 'Refuser la demande',
            html: `
                <form id="reject-form" class="text-left">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mr-4">
                            <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2">
                                Refuser la demande
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Êtes-vous sûr de vouloir refuser cette demande ? Veuillez indiquer la raison du refus ci-dessous.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="bg-red-50 p-4 rounded-md border border-red-100">
                            <label for="reject-commentaire" class="block text-sm font-medium text-red-800 mb-2">Raison du refus <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment-slash"></i>
                                </div>
                                <textarea id="reject-commentaire" name="commentaire" rows="5"
                                    class="pl-10 block w-full py-3 px-4 border border-red-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-base"
                                    placeholder="Veuillez expliquer pourquoi cette demande est refusée..." required></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            `,
            showCancelButton: true,
            confirmButtonText: 'Refuser',
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#64748b',
            customClass: {
                confirmButton: 'btn-hover-effect',
                cancelButton: 'btn-hover-effect'
            },
            focusConfirm: false
        }).then((result) => {
            if (result.isConfirmed) {
                const commentaire = document.getElementById('reject-commentaire').value;

                if (!commentaire) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Veuillez indiquer un motif de refus'
                    });
                    return;
                }

                // Submit rejection form
                const formData = new FormData();
                formData.append('commentaire', commentaire);
                formData.append('_token', window.CSRF_TOKENS.reject_demande);

                fetch(`/demandes_track/public/index.php/validation-box/reject/${id}`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        Toast.fire({
                            icon: 'success',
                            title: 'Demande refusée'
                        });
                        // Reload page after successful rejection
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        throw new Error('Network response was not ok');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toast.fire({
                        icon: 'error',
                        title: 'Une erreur est survenue'
                    });
                });
            }
        });
    };

    window.openProcessModal = function(id) {
        Swal.fire({
            title: 'Marquer comme traitée',
            html: `
                <form id="process-form" class="text-left">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mr-4">
                            <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2">
                                Marquer comme traitée
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Êtes-vous sûr de vouloir marquer cette demande comme traitée ? Vous pouvez ajouter un commentaire ci-dessous.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="bg-green-50 p-4 rounded-md border border-green-100">
                            <label for="process-commentaire" class="block text-sm font-medium text-green-800 mb-2">Commentaire (optionnel)</label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment-dots"></i>
                                </div>
                                <textarea id="process-commentaire" name="commentaire" rows="4"
                                    class="pl-10 block w-full py-3 px-4 border border-green-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-base"
                                    placeholder="Ajoutez un commentaire concernant le traitement de cette demande..."></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            `,
            showCancelButton: true,
            confirmButtonText: 'Confirmer',
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#10b981',
            cancelButtonColor: '#64748b',
            customClass: {
                confirmButton: 'btn-hover-effect',
                cancelButton: 'btn-hover-effect'
            },
            focusConfirm: false
        }).then((result) => {
            if (result.isConfirmed) {
                const commentaire = document.getElementById('process-commentaire').value;

                // Submit process form
                const formData = new FormData();
                formData.append('commentaire', commentaire);
                formData.append('_token', window.CSRF_TOKENS.process_demande);

                fetch(`/demandes_track/public/index.php/assigned-requests/process/${id}`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        Toast.fire({
                            icon: 'success',
                            title: 'Demande traitée'
                        });
                        // Reload page after successful processing
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        throw new Error('Network response was not ok');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toast.fire({
                        icon: 'error',
                        title: 'Une erreur est survenue'
                    });
                });
            }
        });
    };

    window.openCloseModal = function(id) {
        Swal.fire({
            title: 'Clôturer la demande',
            html: `
                <form id="close-form" class="text-left">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 mr-4">
                            <svg class="h-6 w-6 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2">
                                Clôturer la demande
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Êtes-vous sûr de vouloir clôturer cette demande ? Cette action finalise le traitement de la demande.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="bg-purple-50 p-4 rounded-md border border-purple-100">
                            <label for="close-commentaire" class="block text-sm font-medium text-purple-800 mb-2">Commentaire de clôture (optionnel)</label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment-dots"></i>
                                </div>
                                <textarea id="close-commentaire" name="commentaire" rows="4"
                                    class="pl-10 block w-full py-3 px-4 border border-purple-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-base"
                                    placeholder="Ajoutez un commentaire concernant la clôture de cette demande..."></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            `,
            showCancelButton: true,
            confirmButtonText: 'Clôturer',
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#8b5cf6',
            cancelButtonColor: '#64748b',
            customClass: {
                confirmButton: 'btn-hover-effect',
                cancelButton: 'btn-hover-effect'
            },
            focusConfirm: false
        }).then((result) => {
            if (result.isConfirmed) {
                const commentaire = document.getElementById('close-commentaire').value;

                // Préparer les données pour la mise à jour du statut
                const formData = new FormData();
                formData.append('commentaire', commentaire);
                formData.append('statut', 'cloturee');
                formData.append('update_type', 'gestionnaire');
                formData.append('_token', window.CSRF_TOKENS.update_gestionnaire);

                // Envoyer la requête pour clôturer la demande
                fetch(`/demandes_track/public/index.php/demande/${id}/update-status`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error('Network response was not ok');
                })
                .then(data => {
                    if (data.success) {
                        Toast.fire({
                            icon: 'success',
                            title: 'Demande clôturée'
                        });
                        // Reload page after successful closure
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        Toast.fire({
                            icon: 'error',
                            title: data.message || 'Une erreur est survenue'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toast.fire({
                        icon: 'error',
                        title: 'Une erreur est survenue'
                    });
                });
            }
        });
    };
}
