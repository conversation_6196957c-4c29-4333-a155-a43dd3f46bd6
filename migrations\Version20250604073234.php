<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250604073234 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE demande ADD gestionnaire_preferentiel_id INT DEFAULT NULL, ADD a_sortir_en_achat TINYINT(1) DEFAULT NULL, CHANGE compte compte VARCHAR(50) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE demande ADD CONSTRAINT FK_2694D7A513473BD7 FOREIGN KEY (gestionnaire_preferentiel_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2694D7A513473BD7 ON demande (gestionnaire_preferentiel_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE demande_item ADD date_souhaitee DATE DEFAULT NULL, ADD lien_plan VARCHAR(500) DEFAULT NULL, ADD date_previsionelle DATE DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE demande DROP FOREIGN KEY FK_2694D7A513473BD7
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_2694D7A513473BD7 ON demande
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE demande DROP gestionnaire_preferentiel_id, DROP a_sortir_en_achat, CHANGE compte compte VARCHAR(50) NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE demande_item DROP date_souhaitee, DROP lien_plan, DROP date_previsionelle
        SQL);
    }
}
