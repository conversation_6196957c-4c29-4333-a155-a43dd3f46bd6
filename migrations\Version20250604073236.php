<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250604073236 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add new fields to demande and demande_item tables (safe version)';
    }

    public function up(Schema $schema): void
    {
        // Ajouter les nouveaux champs à la table demande seulement s'ils n'existent pas
        $this->addSql('ALTER TABLE demande ADD COLUMN IF NOT EXISTS gestionnaire_preferentiel_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE demande ADD COLUMN IF NOT EXISTS a_sortir_en_achat TINYINT(1) DEFAULT NULL');
        $this->addSql('ALTER TABLE demande MODIFY compte VARCHAR(50) DEFAULT NULL');
        
        // Ajouter la contrainte de clé étrangère seulement si elle n'existe pas
        $this->addSql('
            SET @constraint_exists = (
                SELECT COUNT(*) 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = "demande" 
                AND CONSTRAINT_NAME = "FK_2694D7A5A8B8B5F7"
            );
            SET @sql = IF(@constraint_exists = 0, 
                "ALTER TABLE demande ADD CONSTRAINT FK_2694D7A5A8B8B5F7 FOREIGN KEY (gestionnaire_preferentiel_id) REFERENCES user (id)", 
                "SELECT \'Constraint already exists\' as message"
            );
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
        ');
        
        // Ajouter l'index seulement s'il n'existe pas
        $this->addSql('
            SET @index_exists = (
                SELECT COUNT(*) 
                FROM information_schema.STATISTICS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = "demande" 
                AND INDEX_NAME = "IDX_2694D7A5A8B8B5F7"
            );
            SET @sql = IF(@index_exists = 0, 
                "CREATE INDEX IDX_2694D7A5A8B8B5F7 ON demande (gestionnaire_preferentiel_id)", 
                "SELECT \'Index already exists\' as message"
            );
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
        ');
        
        // Ajouter les nouveaux champs à la table demande_item
        $this->addSql('ALTER TABLE demande_item ADD COLUMN IF NOT EXISTS date_souhaitee DATE DEFAULT NULL');
        $this->addSql('ALTER TABLE demande_item ADD COLUMN IF NOT EXISTS lien_plan VARCHAR(500) DEFAULT NULL');
        $this->addSql('ALTER TABLE demande_item ADD COLUMN IF NOT EXISTS date_previsionelle DATE DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE demande DROP FOREIGN KEY IF EXISTS FK_2694D7A5A8B8B5F7');
        $this->addSql('DROP INDEX IF EXISTS IDX_2694D7A5A8B8B5F7 ON demande');
        $this->addSql('ALTER TABLE demande DROP COLUMN IF EXISTS gestionnaire_preferentiel_id');
        $this->addSql('ALTER TABLE demande DROP COLUMN IF EXISTS a_sortir_en_achat');
        $this->addSql('ALTER TABLE demande MODIFY compte VARCHAR(50) NOT NULL');
        
        $this->addSql('ALTER TABLE demande_item DROP COLUMN IF EXISTS date_souhaitee');
        $this->addSql('ALTER TABLE demande_item DROP COLUMN IF EXISTS lien_plan');
        $this->addSql('ALTER TABLE demande_item DROP COLUMN IF EXISTS date_previsionelle');
    }
}
