import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ["container", "template", "item"];

    connect() {
        this.index = this.itemTargets.length;
    }

    addItem(event) {
        event.preventDefault();

        const prototype = this.templateTarget.dataset.prototype;
        const newForm = prototype.replace(/__name__/g, this.index);

        // Create a temporary div to parse the form
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = newForm;

        // Create the main item div
        const itemDiv = document.createElement('div');
        itemDiv.classList.add('item-row', 'flex', 'items-start', 'space-x-2', 'mb-3', 'p-3', 'border', 'border-gray-200', 'rounded-md');
        itemDiv.dataset.target = "demande-item.item";

        // Create reference div
        const referenceDiv = document.createElement('div');
        referenceDiv.classList.add('flex-1');

        // Find reference label, input and errors
        const referenceLabel = tempDiv.querySelector('label[for$="_reference"]');
        const referenceInput = tempDiv.querySelector('input[id$="_reference"]');
        const referenceErrors = tempDiv.querySelector('div:has(input[id$="_reference"]) + div.invalid-feedback');

        if (referenceLabel) referenceDiv.appendChild(referenceLabel);
        if (referenceInput) referenceDiv.appendChild(referenceInput);
        if (referenceErrors) referenceDiv.appendChild(referenceErrors);

        // Create quantity div
        const quantiteDiv = document.createElement('div');
        quantiteDiv.classList.add('w-24');

        // Find quantity label, input and errors
        const quantiteLabel = tempDiv.querySelector('label[for$="_quantite"]');
        const quantiteInput = tempDiv.querySelector('input[id$="_quantite"]');
        const quantiteErrors = tempDiv.querySelector('div:has(input[id$="_quantite"]) + div.invalid-feedback');

        if (quantiteLabel) quantiteDiv.appendChild(quantiteLabel);
        if (quantiteInput) quantiteDiv.appendChild(quantiteInput);
        if (quantiteErrors) quantiteDiv.appendChild(quantiteErrors);

        // Add the divs to the item row
        itemDiv.appendChild(referenceDiv);
        itemDiv.appendChild(quantiteDiv);

        // Add delete button
        const deleteButton = document.createElement('button');
        deleteButton.type = 'button';
        deleteButton.classList.add('mt-7', 'p-2', 'bg-red-500', 'text-white', 'rounded-md', 'hover:bg-red-600', 'focus:outline-none', 'focus:ring-2', 'focus:ring-offset-2', 'focus:ring-red-500');
        deleteButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg>';
        deleteButton.dataset.action = "click->demande-item#removeItem";

        itemDiv.appendChild(deleteButton);

        this.containerTarget.appendChild(itemDiv);

        this.index++;
    }

    removeItem(event) {
        event.preventDefault();

        const item = event.currentTarget.closest('[data-target="demande-item.item"]');
        item.remove();
    }
}
