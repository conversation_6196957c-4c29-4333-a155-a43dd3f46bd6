<?php

namespace App\Command;

use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:update-user-roles',
    description: 'Met à jour les rôles d\'un utilisateur',
)]
class UpdateUserRolesCommand extends Command
{
    private $userRepository;
    private $entityManager;

    public function __construct(UserRepository $userRepository, EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->userRepository = $userRepository;
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->addArgument('username', InputArgument::REQUIRED, 'Nom d\'utilisateur')
            ->addArgument('roles', InputArgument::IS_ARRAY, 'Rôles à ajouter (séparés par des espaces)')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $username = $input->getArgument('username');
        $rolesToAdd = $input->getArgument('roles');

        $user = $this->userRepository->findOneBy(['username' => $username]);

        if (!$user) {
            $io->error(sprintf('L\'utilisateur "%s" n\'existe pas.', $username));
            return Command::FAILURE;
        }

        $currentRoles = $user->getRoles();
        // Supprimer ROLE_USER qui est ajouté automatiquement
        $currentRoles = array_diff($currentRoles, ['ROLE_USER']);
        
        $io->info(sprintf('Rôles actuels de l\'utilisateur "%s": %s', $username, implode(', ', $currentRoles)));

        if (empty($rolesToAdd)) {
            // Si aucun rôle n'est spécifié, ajouter les rôles administratifs par défaut
            $rolesToAdd = [
                'ROLE_USER_APPROVISIONNEMENT',
                'ROLE_ADMIN'
            ];
            $io->info('Aucun rôle spécifié, ajout des rôles administratifs par défaut.');
        }

        // Fusionner les rôles actuels avec les nouveaux rôles
        $newRoles = array_unique(array_merge($currentRoles, $rolesToAdd));
        
        // Mettre à jour les rôles de l'utilisateur
        $user->setRoles($newRoles);
        $this->entityManager->flush();

        $io->success(sprintf('Les rôles de l\'utilisateur "%s" ont été mis à jour: %s', $username, implode(', ', $newRoles)));

        return Command::SUCCESS;
    }
}
