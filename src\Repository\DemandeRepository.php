<?php

namespace App\Repository;

use App\Entity\Demande;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Demande>
 *
 * @method Demande|null find($id, $lockMode = null, $lockVersion = null)
 * @method Demande|null findOneBy(array $criteria, array $orderBy = null)
 * @method Demande[]    findAll()
 * @method Demande[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DemandeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Demande::class);
    }

    public function save(Demande $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Demande $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Trouve les demandes avec filtres
     */
    public function findByFilters(array $filters, User $user = null): array
    {
        $qb = $this->createQueryBuilder('d')
            ->leftJoin('d.items', 'i')
            ->addSelect('i');

        if ($user && !$user->isAdmin()) {
            $qb->andWhere('d.demandeur = :user OR d.technicien = :user')
               ->setParameter('user', $user);
        }

        if (!empty($filters['statut'])) {
            $qb->andWhere('d.statut = :statut')
               ->setParameter('statut', $filters['statut']);
        }

        if (!empty($filters['urgence'])) {
            $qb->andWhere('d.urgence = :urgence')
               ->setParameter('urgence', $filters['urgence']);
        }

        if (!empty($filters['nature'])) {
            $qb->andWhere('d.nature = :nature')
               ->setParameter('nature', $filters['nature']);
        }

        if (!empty($filters['search'])) {
            $qb->leftJoin('d.personneConcernee', 'p')
               ->andWhere('i.reference LIKE :search OR p.nom LIKE :search OR p.prenom LIKE :search OR d.destinataire LIKE :search')
               ->setParameter('search', '%' . $filters['search'] . '%');
        }

        $qb->orderBy('d.dateCreation', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les demandes en attente de validation
     */
    public function findPendingDemandes(): array
    {
        return $this->createQueryBuilder('d')
            ->leftJoin('d.items', 'i')
            ->addSelect('i')
            ->andWhere('d.statut = :statut')
            ->setParameter('statut', Demande::STATUT_EN_ATTENTE)
            ->orderBy('d.dateCreation', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Compte le nombre de demandes en attente de validation
     */
    public function countPendingDemandes(): int
    {
        return $this->createQueryBuilder('d')
            ->select('COUNT(d.id)')
            ->andWhere('d.statut = :statut')
            ->setParameter('statut', Demande::STATUT_EN_ATTENTE)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Trouve les demandes assignées à un gestionnaire (validées et traitées)
     */
    public function findAssignedDemandes(User $technicien): array
    {
        return $this->createQueryBuilder('d')
            ->leftJoin('d.items', 'i')
            ->addSelect('i')
            ->andWhere('d.technicien = :technicien')
            ->andWhere('d.statut IN (:statuts)')
            ->setParameter('technicien', $technicien)
            ->setParameter('statuts', [Demande::STATUT_VALIDEE, Demande::STATUT_TRAITEE])
            ->orderBy('d.dateCreation', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve toutes les demandes assignées à un gestionnaire (y compris traitées)
     */
    public function findAllAssignedDemandes(User $technicien): array
    {
        return $this->createQueryBuilder('d')
            ->leftJoin('d.items', 'i')
            ->addSelect('i')
            ->andWhere('d.technicien = :technicien')
            ->andWhere('d.statut IN (:statuts)')
            ->setParameter('technicien', $technicien)
            ->setParameter('statuts', [Demande::STATUT_VALIDEE, Demande::STATUT_TRAITEE])
            ->orderBy('d.dateCreation', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Compte le nombre de demandes assignées à un gestionnaire (validées et traitées)
     */
    public function countAssignedDemandes(User $technicien): int
    {
        return $this->createQueryBuilder('d')
            ->select('COUNT(d.id)')
            ->andWhere('d.technicien = :technicien')
            ->andWhere('d.statut IN (:statuts)')
            ->setParameter('technicien', $technicien)
            ->setParameter('statuts', [Demande::STATUT_VALIDEE, Demande::STATUT_TRAITEE])
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Trouve toutes les demandes assignées à tous les gestionnaires (pour les admins)
     */
    public function findAllAssignedDemandesForAdmin(): array
    {
        return $this->createQueryBuilder('d')
            ->leftJoin('d.items', 'i')
            ->leftJoin('d.technicien', 't')
            ->addSelect('i')
            ->addSelect('t')
            ->andWhere('d.statut IN (:statuts)')
            ->setParameter('statuts', [Demande::STATUT_VALIDEE, Demande::STATUT_TRAITEE])
            ->orderBy('d.dateCreation', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve l'historique de toutes les demandes traitées (pour les admins)
     */
    public function findAllProcessedDemandesForAdmin(): array
    {
        return $this->createQueryBuilder('d')
            ->leftJoin('d.items', 'i')
            ->leftJoin('d.technicien', 't')
            ->addSelect('i')
            ->addSelect('t')
            ->andWhere('d.statut = :statut')
            ->setParameter('statut', Demande::STATUT_TRAITEE)
            ->orderBy('d.dateCreation', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Compte le nombre total de demandes assignées (pour les admins)
     */
    public function countAllAssignedDemandesForAdmin(): int
    {
        return $this->createQueryBuilder('d')
            ->select('COUNT(d.id)')
            ->andWhere('d.statut IN (:statuts)')
            ->setParameter('statuts', [Demande::STATUT_VALIDEE, Demande::STATUT_TRAITEE])
            ->getQuery()
            ->getSingleScalarResult();
    }
}
