<?php

namespace App\Controller;

use App\Entity\Demande;
use App\Repository\DemandeRepository;
use App\Service\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/assigned-requests')]
#[IsGranted('ROLE_USER')]
class AssignedRequestsController extends AbstractController
{
    #[Route('/', name: 'app_assigned_requests_index', methods: ['GET'])]
    public function index(DemandeRepository $demandeRepository): Response
    {
        $user = $this->getUser();

        // Si l'utilisateur est admin, récupérer toutes les demandes assignées
        if ($user->isAdmin()) {
            $assignedDemandes = $demandeRepository->findAllAssignedDemandesForAdmin();
        } else {
            // Sinon, récupérer uniquement les demandes assignées à cet utilisateur
            $assignedDemandes = $demandeRepository->findAssignedDemandes($user);
        }

        return $this->render('assigned_requests/index.html.twig', [
            'demandes' => $assignedDemandes,
            'isAdmin' => $user->isAdmin(),
        ]);
    }

    #[Route('/history', name: 'app_assigned_requests_history', methods: ['GET'])]
    public function history(DemandeRepository $demandeRepository): Response
    {
        $user = $this->getUser();

        // Si l'utilisateur est admin, récupérer toutes les demandes traitées
        if ($user->isAdmin()) {
            $processedDemandes = $demandeRepository->findAllProcessedDemandesForAdmin();
        } else {
            // Sinon, récupérer uniquement les demandes traitées assignées à cet utilisateur
            $allAssignedDemandes = $demandeRepository->findAllAssignedDemandes($user);

            // Filtrer pour ne garder que les demandes traitées
            $processedDemandes = array_filter($allAssignedDemandes, function($demande) {
                return $demande->getStatut() === Demande::STATUT_TRAITEE;
            });
        }

        return $this->render('assigned_requests/history.html.twig', [
            'demandes' => $processedDemandes,
            'isAdmin' => $user->isAdmin(),
        ]);
    }

    #[Route('/{id}/process', name: 'app_assigned_requests_process', methods: ['POST'])]
    public function process(Request $request, Demande $demande, EntityManagerInterface $entityManager, EmailService $emailService): Response
    {
        $user = $this->getUser();

        // Vérifier que l'utilisateur est bien le gestionnaire assigné ou un admin
        if ($demande->getTechnicien() !== $user && !$user->isAdmin()) {
            throw $this->createAccessDeniedException('Vous n\'êtes pas autorisé à traiter cette demande.');
        }

        // Vérifier le token CSRF
        if (!$this->isCsrfTokenValid('process_demande', $request->request->get('_token'))) {
            $this->addFlash('error', 'Erreur de sécurité.');
            return $this->redirectToRoute('app_assigned_requests_index');
        }

        // Sauvegarder l'ancien statut pour l'email
        $oldStatus = $demande->getStatut();

        // Mettre à jour le statut de la demande
        $demande->setStatut(Demande::STATUT_TRAITEE);

        // Ajouter un commentaire si fourni
        $commentaire = $request->request->get('commentaire');
        if ($commentaire) {
            $demande->setCommentaireGestionnaire(
                ($demande->getCommentaireGestionnaire() ? $demande->getCommentaireGestionnaire() . "\n\n" : '') .
                "Traité par " . $this->getUser()->getNomComplet() . " le " . (new \DateTime())->format('d/m/Y H:i') . ":\n" .
                $commentaire
            );
        }

        $entityManager->flush();

        // Envoyer un email pour notifier du changement de statut
        $emailService->sendDemandeStatusChangedEmail($demande, $oldStatus);

        $this->addFlash('success', 'Demande traitée.');

        return $this->redirectToRoute('app_assigned_requests_index');
    }

    #[Route('/update/{id}', name: 'app_assigned_requests_update', methods: ['POST'])]
    public function update(Request $request, Demande $demande, EntityManagerInterface $entityManager): Response
    {
        try {
            // Debug log
            error_log('Update method called for demande ID: ' . $demande->getId());
            error_log('Request method: ' . $request->getMethod());
            error_log('Is AJAX: ' . ($request->isXmlHttpRequest() ? 'yes' : 'no'));
            error_log('CSRF Token: ' . $request->request->get('_token'));

            $user = $this->getUser();

            // Vérifier que l'utilisateur est bien le gestionnaire assigné ou un admin
            error_log('User ID: ' . ($user ? $user->getId() : 'null'));
            error_log('Technicien ID: ' . ($demande->getTechnicien() ? $demande->getTechnicien()->getId() : 'null'));
            error_log('Is Admin: ' . ($user && $user->isAdmin() ? 'yes' : 'no'));

            // Temporarily disable user validation for debugging
            /*
            if ($demande->getTechnicien() !== $user && !$user->isAdmin()) {
                throw $this->createAccessDeniedException('Vous n\'êtes pas autorisé à modifier cette demande.');
            }
            */

            // Vérifier que la demande est bien validée
            error_log('Demande statut: ' . $demande->getStatut());
            error_log('Expected statut: ' . Demande::STATUT_VALIDEE);

            // Temporarily disable status validation for debugging
            /*
            if ($demande->getStatut() !== Demande::STATUT_VALIDEE) {
                throw $this->createAccessDeniedException('Vous ne pouvez modifier que des demandes validées.');
            }
            */

            // Vérifier le token CSRF
            $tokenId = 'update_gestionnaire_' . $demande->getId();
            $submittedToken = $request->request->get('_token');

            error_log('CSRF Token ID: ' . $tokenId);
            error_log('Submitted Token: ' . $submittedToken);

            $isValid = $this->isCsrfTokenValid($tokenId, $submittedToken);
            error_log('CSRF validation result: ' . ($isValid ? 'valid' : 'invalid'));

            if (!$isValid) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse(['success' => false, 'message' => 'Erreur de sécurité.'], 400);
                }
                $this->addFlash('error', 'Erreur de sécurité.');
                return $this->redirectToRoute('app_demande_show', ['id' => $demande->getId()]);
            }

            // Récupérer les données du formulaire
            $wbs = $request->request->get('wbs');
            $dateGestionnaire = $request->request->get('dateGestionnaire');
            $commentaire = $request->request->get('commentaire');

            // Debug log
            error_log('Request parameters:');
            error_log('WBS: ' . $wbs);
            error_log('Date Gestionnaire: ' . $dateGestionnaire);
            error_log('Commentaire: ' . $commentaire);

            // Mettre à jour le WBS
            if ($wbs !== null) {
                $demande->setWbs($wbs);
            }

            // Mettre à jour la date gestionnaire
            if ($dateGestionnaire !== null && $dateGestionnaire !== '') {
                try {
                    $demande->setDateGestionnaire(new \DateTime($dateGestionnaire));
                    error_log('Date gestionnaire set to: ' . $dateGestionnaire);
                } catch (\Exception $e) {
                    error_log('Error parsing date: ' . $e->getMessage());
                    $demande->setDateGestionnaire(null);
                }
            } else {
                $demande->setDateGestionnaire(null);
                error_log('Date gestionnaire set to null');
            }

            // Vérifier si un commentaire a été fourni
            $commentaireAdded = false;
            if ($commentaire && trim($commentaire) !== '') {
                $demande->setCommentaireGestionnaire(
                    ($demande->getCommentaireGestionnaire() ? $demande->getCommentaireGestionnaire() . "\n\n" : '') .
                    "Mise à jour par " . $this->getUser()->getNomComplet() . " le " . (new \DateTime())->format('d/m/Y H:i') . ":\n" .
                    $commentaire
                );
                $commentaireAdded = true;
            }

            $entityManager->flush();

            // Return JSON response for AJAX requests
            if ($request->isXmlHttpRequest()) {
                $response = [
                    'success' => true,
                    'wbs' => $demande->getWbs(),
                    'dateGestionnaire' => $demande->getDateGestionnaire() ? $demande->getDateGestionnaire()->format('d/m/Y') : null,
                    'commentaire' => $demande->getCommentaireGestionnaire(),
                    'commentaireAdded' => $commentaireAdded,
                    'id' => $demande->getId()
                ];

                // Debug log
                error_log('Response: ' . json_encode($response));

                return new JsonResponse($response);
            }

            $this->addFlash('success', 'Demande mise à jour.');
            return $this->redirectToRoute('app_demande_show', ['id' => $demande->getId()]);
        } catch (\Exception $e) {
            error_log('Exception: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Une erreur est survenue: ' . $e->getMessage()
                ], 500);
            }

            $this->addFlash('error', 'Une erreur est survenue: ' . $e->getMessage());
            return $this->redirectToRoute('app_demande_show', ['id' => $demande->getId()]);
        }
    }

    #[Route('/count', name: 'app_assigned_requests_count', methods: ['GET'])]
    public function count(DemandeRepository $demandeRepository): Response
    {
        $user = $this->getUser();

        // Si l'utilisateur est admin, compter toutes les demandes assignées
        if ($user->isAdmin()) {
            $count = $demandeRepository->countAllAssignedDemandesForAdmin();
        } else {
            // Sinon, compter uniquement les demandes assignées à cet utilisateur
            $count = $demandeRepository->countAssignedDemandes($user);
        }

        return $this->json(['count' => $count]);
    }
}
