<?php

namespace App\Entity;

use App\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[UniqueEntity(fields: ['email'], message: 'Il existe déjà un compte avec cet email')]
class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 180, unique: true)]
    private ?string $email = null;

    #[ORM\Column]
    private array $roles = [];

    /**
     * @var string The hashed password
     */
    #[ORM\Column(nullable: true)]
    private ?string $password = null;

    #[ORM\Column(length: 255)]
    private ?string $nom = null;

    #[ORM\Column(length: 255)]
    private ?string $prenom = null;

    #[ORM\Column(length: 255, unique: true, nullable: true)]
    private ?string $username = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $secteur = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $manager = null;

    #[ORM\Column(nullable: true)]
    private ?bool $isManager = false;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $titre = null;

    #[ORM\Column(nullable: true)]
    private ?bool $vpn = false;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $mobile = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $telephoneNumber = null;

    #[ORM\OneToMany(mappedBy: 'demandeur', targetEntity: Demande::class)]
    private Collection $demandes;

    #[ORM\OneToMany(mappedBy: 'technicien', targetEntity: Demande::class)]
    private Collection $demandesAssignees;

    public function __construct()
    {
        $this->demandes = new ArrayCollection();
        $this->demandesAssignees = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): static
    {
        $this->email = $email;

        return $this;
    }

    /**
     * A visual identifier that represents this user.
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) ($this->username ?? $this->email);
    }

    /**
     * @see UserInterface
     */
    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER
        $roles[] = 'ROLE_USER';

        // Ajouter tous les rôles avancés à l'utilisateur eerdmann
        if ($this->username === 'eerdmann') {
            $roles[] = 'ROLE_USER_APPROVISIONNEMENT';
            $roles[] = 'ROLE_ADMIN';
        }

        // Ajouter ROLE_ADMIN aux utilisateurs ayant des rôles avancés
        $advancedRoles = [
            'ROLE_USER_APPROVISIONNEMENT',
            'ROLE_MANAGER_GAP',
            'ROLE_MANAGER_USINAGE',
            'ROLE_MANAGER_DIRECTION',
            'ROLE_MANAGER_SUPPLY_CHAIN'
        ];

        foreach ($advancedRoles as $role) {
            if (in_array($role, $roles)) {
                $roles[] = 'ROLE_ADMIN';
                break;
            }
        }

        return array_unique($roles);
    }

    public function setRoles(array $roles): static
    {
        $this->roles = $roles;

        return $this;
    }

    /**
     * @see PasswordAuthenticatedUserInterface
     */
    public function getPassword(): string
    {
        return $this->password ?? '';
    }

    public function setPassword(?string $password): static
    {
        $this->password = $password;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(string $nom): static
    {
        $this->nom = $nom;

        return $this;
    }

    public function getPrenom(): ?string
    {
        return $this->prenom;
    }

    public function setPrenom(string $prenom): static
    {
        $this->prenom = $prenom;

        return $this;
    }

    /**
     * @return Collection<int, Demande>
     */
    public function getDemandes(): Collection
    {
        return $this->demandes;
    }

    public function addDemande(Demande $demande): static
    {
        if (!$this->demandes->contains($demande)) {
            $this->demandes->add($demande);
            $demande->setDemandeur($this);
        }

        return $this;
    }

    public function removeDemande(Demande $demande): static
    {
        if ($this->demandes->removeElement($demande)) {
            // set the owning side to null (unless already changed)
            if ($demande->getDemandeur() === $this) {
                $demande->setDemandeur(null);
            }
        }

        return $this;
    }

    public function getNomComplet(): string
    {
        return $this->prenom . ' ' . $this->nom;
    }

    public function getUsername(): ?string
    {
        return $this->username;
    }

    public function setUsername(?string $username): static
    {
        $this->username = $username;

        return $this;
    }

    public function getSecteur(): ?string
    {
        return $this->secteur;
    }

    public function setSecteur(?string $secteur): static
    {
        $this->secteur = $secteur;

        return $this;
    }

    public function getManager(): ?string
    {
        return $this->manager;
    }

    public function setManager(?string $manager): static
    {
        $this->manager = $manager;

        return $this;
    }

    public function isIsManager(): ?bool
    {
        return $this->isManager;
    }

    /**
     * Vérifie si l'utilisateur a des droits d'accès avancés
     * basés sur des rôles spécifiques
     */
    public function hasAdvancedAccess(): bool
    {
        // Si le username est fkleindienst, retourner false indépendamment des rôles
        if ($this->username === 'fkleindienst') {
            return false;
        }

        if ($this->username === 'eerdmann') {
            return true;
        }

        $advancedRoles = [
            'ROLE_USER_APPROVISIONNEMENT',
            'ROLE_MANAGER_GAP',
            'ROLE_MANAGER_USINAGE',
            'ROLE_MANAGER_DIRECTION',
            'ROLE_MANAGER_SUPPLY_CHAIN',
            'ROLE_ADMIN'
        ];

        foreach ($advancedRoles as $role) {
            if (in_array($role, $this->getRoles())) {
                return true;
            }
        }

        return false;
    }

    /**
     * Vérifie si l'utilisateur a des droits d'administrateur
     * Les utilisateurs avec des rôles avancés ont également des droits d'administrateur
     */
    public function isAdmin(): bool
    {
        return $this->hasAdvancedAccess() || in_array('ROLE_ADMIN', $this->getRoles());
    }

    /**
     * @deprecated Utiliser hasAdvancedAccess() à la place
     */
    public function hasManagerAccess(): bool
    {
        return $this->hasAdvancedAccess();
    }

    public function setIsManager(?bool $isManager): static
    {
        $this->isManager = $isManager;

        return $this;
    }

    public function getTitre(): ?string
    {
        return $this->titre;
    }

    public function setTitre(?string $titre): static
    {
        $this->titre = $titre;

        return $this;
    }

    public function isVpn(): ?bool
    {
        return $this->vpn;
    }

    public function setVpn(?bool $vpn): static
    {
        $this->vpn = $vpn;

        return $this;
    }

    public function getMobile(): ?string
    {
        return $this->mobile;
    }

    public function setMobile(?string $mobile): static
    {
        $this->mobile = $mobile;

        return $this;
    }

    public function getTelephoneNumber(): ?string
    {
        return $this->telephoneNumber;
    }

    public function setTelephoneNumber(?string $telephoneNumber): static
    {
        $this->telephoneNumber = $telephoneNumber;

        return $this;
    }

    /**
     * Alias pour getDemandesAssignees() pour compatibilité
     * @return Collection<int, Demande>
     */
    public function getDemandesConcernees(): Collection
    {
        return $this->demandesAssignees;
    }

    /**
     * Alias pour addDemandeAssignee() pour compatibilité
     */
    public function addDemandeConcernee(Demande $demande): static
    {
        return $this->addDemandeAssignee($demande);
    }

    /**
     * Alias pour removeDemandeAssignee() pour compatibilité
     */
    public function removeDemandeConcernee(Demande $demande): static
    {
        return $this->removeDemandeAssignee($demande);
    }

    /**
     * @return Collection<int, Demande>
     */
    public function getDemandesAssignees(): Collection
    {
        return $this->demandesAssignees;
    }

    public function addDemandeAssignee(Demande $demande): static
    {
        if (!$this->demandesAssignees->contains($demande)) {
            $this->demandesAssignees->add($demande);
            $demande->setTechnicien($this);
        }

        return $this;
    }

    public function removeDemandeAssignee(Demande $demande): static
    {
        if ($this->demandesAssignees->removeElement($demande)) {
            // set the owning side to null (unless already changed)
            if ($demande->getTechnicien() === $this) {
                $demande->setTechnicien(null);
            }
        }

        return $this;
    }
}
