<?php

namespace App\Controller;

use App\Repository\DemandeRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/export')]
#[IsGranted('ROLE_ADMIN')]
class ExportController extends AbstractController
{
    #[Route('/demandes/csv', name: 'app_export_demandes_csv', methods: ['GET'])]
    public function exportDemandesCSV(DemandeRepository $demandeRepository): Response
    {
        // Récupérer toutes les demandes
        $demandes = $demandeRepository->findAll();

        // Créer le contenu CSV
        $csvContent = $this->generateCSVContent($demandes);

        // C<PERSON>er la réponse
        $response = new Response($csvContent);

        // Configurer les en-têtes pour le téléchargement
        $disposition = $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            'demandes_' . date('Y-m-d_H-i-s') . '.csv'
        );

        $response->headers->set('Content-Disposition', $disposition);
        $response->headers->set('Content-Type', 'text/csv; charset=UTF-8');

        return $response;
    }

    private function generateCSVContent(array $demandes): string
    {
        // Définir les en-têtes CSV
        $headers = [
            'ID',
            'Date de création',
            'Date souhaitée',
            'Demandeur',
            'Gestionnaire',
            'Urgence',
            'Nature',
            'Destinataire',
            'Compte',
            'WBS',
            'Numéro OF',
            'Potentiel',
            'Nom Client',
            'Statut',
            'Commentaire demandeur',
            'Commentaire gestionnaire',
            'Items'
        ];

        // Créer le contenu CSV
        $content = implode(';', $headers) . "\n";

        foreach ($demandes as $demande) {
            $row = [
                $demande->getId(),
                $demande->getDateCreation()->format('d/m/Y'),
                $demande->getDateSouhaitee()->format('d/m/Y'),
                $demande->getDemandeur()->getNomComplet(),
                $demande->getTechnicien() ? $demande->getTechnicien()->getNomComplet() : '',
                $demande->getUrgenceLabel(),
                $demande->getNatureLabel(),
                $demande->getDestinataireComplet(),
                $demande->getCompte(),
                $demande->getWbs() ?: '',
                $demande->getNumeroOF() ?: '',
                $demande->getPotentielLabel() ?: '',
                $demande->getNomClient() ?: '',
                $demande->getStatutLabel(),
                str_replace(["\r", "\n", ";"], [" ", " ", ","], $demande->getCommentaireDemandeur() ?: ''),
                str_replace(["\r", "\n", ";"], [" ", " ", ","], $demande->getCommentaireGestionnaire() ?: ''),
                $this->formatItems($demande->getItems()->toArray())
            ];

            $content .= implode(';', $row) . "\n";
        }

        // Ajouter BOM pour UTF-8
        return "\xEF\xBB\xBF" . $content;
    }

    private function formatItems(array $items): string
    {
        $formattedItems = [];

        foreach ($items as $item) {
            $formattedItems[] = $item->getReference() . ' (x' . $item->getQuantite() . ')';
        }

        return implode(', ', $formattedItems);
    }
}
