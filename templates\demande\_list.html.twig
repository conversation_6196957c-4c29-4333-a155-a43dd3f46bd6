{% if demandes|length > 0 %}
    <div id="table-container" class="overflow-x-auto">
        <table id="demandes-table" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">ID</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Nature</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">N° OF</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Destinataire</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Gestionnaire</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Urgence</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Statut</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Items</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for demande in demandes %}
                    {% set searchableContent = [
                        demande.dateCreation|date('d/m/Y'),
                        demande.natureLabel,
                        demande.destinataire,
                        demande.technicien ? demande.technicien.nomComplet : '',
                        demande.urgenceLabel,
                        demande.statutLabel,
                        demande.compte,
                        demande.commentaireDemandeur,
                        demande.commentaireGestionnaire
                    ]|join(' ')|lower %}

                    {% set itemReferences = [] %}
                    {% for item in demande.items %}
                        {% set itemReferences = itemReferences|merge([item.reference]) %}
                    {% endfor %}

                    {% set searchableContent = searchableContent ~ ' ' ~ itemReferences|join(' ')|lower %}

                    <tr
                        data-statut="{{ demande.statut }}"
                        data-urgence="{{ demande.urgence }}"
                        data-nature="{{ demande.nature }}"
                        data-technicien-id="{{ demande.technicien ? demande.technicien.id : '' }}"
                        data-searchable="{{ searchableContent }}"
                        class="hover:bg-gray-50 transition-colors duration-150"
                    >
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">#{{ demande.id }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-700">{{ demande.dateCreation|date('d/m/Y') }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-700">
                                {{ demande.natureLabel }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-700">
                                {% if demande.numeroOF %}
                                    {{ demande.numeroOF }}
                                {% else %}
                                    <span class="text-gray-400">-</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-700">
                                {{ demande.destinataire }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if demande.technicien %}
                                <div class="text-sm text-gray-700">
                                    {{ demande.technicien.nomComplet }}
                                </div>
                            {% else %}
                                <span class="text-sm text-gray-400 italic">
                                    Non assigné
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if demande.urgence == 'haute' %}
                                <span class="px-3 py-1.5 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 border border-red-200">
                                    <i class="fas fa-exclamation-circle mr-1.5"></i>
                                    {{ demande.urgenceLabel }}
                                </span>
                            {% elseif demande.urgence == 'moyenne' %}
                                <span class="px-3 py-1.5 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200">
                                    <i class="fas fa-exclamation mr-1.5"></i>
                                    {{ demande.urgenceLabel }}
                                </span>
                            {% else %}
                                <span class="px-3 py-1.5 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 border border-green-200">
                                    <i class="fas fa-check-circle mr-1.5"></i>
                                    {{ demande.urgenceLabel }}
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if demande.statut == 'en_attente' %}
                                <span class="px-3 py-1.5 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200">
                                    <i class="fas fa-clock mr-1.5"></i>
                                    {{ demande.statutLabel }}
                                </span>
                            {% elseif demande.statut == 'validee' %}
                                <span class="px-3 py-1.5 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                                    <i class="fas fa-check mr-1.5"></i>
                                    {{ demande.statutLabel }}
                                </span>
                            {% elseif demande.statut == 'refusee' %}
                                <span class="px-3 py-1.5 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 border border-red-200">
                                    <i class="fas fa-times mr-1.5"></i>
                                    {{ demande.statutLabel }}
                                </span>
                            {% elseif demande.statut == 'cloturee' %}
                                <span class="px-3 py-1.5 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800 border border-purple-200">
                                    <i class="fas fa-archive mr-1.5"></i>
                                    {{ demande.statutLabel }}
                                </span>
                            {% else %}
                                <span class="px-3 py-1.5 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 border border-green-200">
                                    <i class="fas fa-check-double mr-1.5"></i>
                                    {{ demande.statutLabel }}
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-700">
                                {{ demande.items|length }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex space-x-3">
                                <a href="{{ path('app_demande_show', {'id': demande.id}) }}" class="inline-flex items-center p-2 text-sm text-gray-600 hover:text-primary hover:bg-blue-50 rounded-full transition-colors duration-200" title="Voir les détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if demande.statut not in ['traitee', 'refusee', 'cloturee'] %}
                                    <a href="{{ path('app_demande_edit', {'id': demande.id}) }}" class="inline-flex items-center p-2 text-sm text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-full transition-colors duration-200" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                {% endif %}
                                {% if demande.statut == 'en_attente' %}
                                    <a href="#" onclick="confirmDelete({{ demande.id }}, event)" class="inline-flex items-center p-2 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors duration-200" title="Supprimer">
                                        <i class="fas fa-trash-alt"></i>
                                    </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div id="no-results-message" class="px-8 py-12 text-center hidden">
        <div class="inline-block p-6 bg-gray-50 rounded-lg border border-gray-200 shadow-sm">
            <div class="text-yellow-500 mb-4">
                <i class="fas fa-search fa-3x"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-700 mb-2">Aucun résultat trouvé</h3>
            <p class="text-gray-500 mb-4">Aucune demande ne correspond à vos critères de recherche.</p>
            <a href="#" id="reset-filters-link" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200">
                <i class="fas fa-sync-alt mr-2"></i>
                Réinitialiser les filtres
            </a>
        </div>
    </div>
{% else %}
    <div class="px-8 py-12 text-center">
        <div class="inline-block p-6 bg-gray-50 rounded-lg border border-gray-200 shadow-sm">
            <div class="text-blue-500 mb-4">
                <i class="fas fa-clipboard-list fa-3x"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-700 mb-2">Aucune demande trouvée</h3>
            <p class="text-gray-500 mb-4">Il n'y a actuellement aucune demande dans le système.</p>
            <a href="{{ path('app_demande_new') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200">
                <i class="fas fa-plus-circle mr-2"></i>
                Créer une nouvelle demande
            </a>
        </div>
    </div>
{% endif %}
