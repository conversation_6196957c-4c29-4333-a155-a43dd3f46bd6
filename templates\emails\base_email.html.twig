<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{% block title %}{% endblock %}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 650px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border: 1px solid #ddd;
        }
        .header {
            border-bottom: 2px solid #003366;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 22px;
            color: #003366;
        }
        h1 {
            color: #003366;
            margin-top: 0;
            font-size: 20px;
        }
        h2 {
            color: #003366;
            font-size: 16px;
            margin-top: 20px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        p {
            margin-bottom: 15px;
        }
        .info-box {
            background-color: #f9f9f9;
            border-left: 4px solid #003366;
            padding: 12px 15px;
            margin-bottom: 20px;
        }
        .info-box h2 {
            margin-top: 0;
            border-bottom: none;
        }
        ul {
            padding-left: 20px;
            margin: 10px 0;
        }
        li {
            margin-bottom: 8px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #777;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #003366;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin: 15px 0;
        }
        .btn:hover {
            background-color: #004080;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 13px;
            font-weight: bold;
            color: #000000;
            border: 1px solid #cccccc;
        }
        .status-waiting {
            background-color: #ffeaa7;
            border-color: #f39c12;
        }
        .status-validated {
            background-color: #c4f5d8;
            border-color: #27ae60;
        }
        .status-refused {
            background-color: #ffd1cc;
            border-color: #e74c3c;
        }
        .status-processed {
            background-color: #c6e2ff;
            border-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Commandes internes</h1>
        </div>

        {% block body %}{% endblock %}

        <div class="footer">
            <p>Cet email a été envoyé automatiquement - SCM Le Mans</p>
        </div>
    </div>
</body>
</html>
