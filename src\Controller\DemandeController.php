<?php

namespace App\Controller;

use App\Entity\Demande;
use App\Entity\DemandeItem;
use App\Form\DemandeType;
use App\Repository\DemandeRepository;
use App\Repository\UserRepository;
use App\Service\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/demande')]
class DemandeController extends AbstractController
{
    #[Route('/', name: 'app_demande_index', methods: ['GET'])]
    public function index(Request $request, DemandeRepository $demandeRepository, UserRepository $userRepository): Response
    {
        $filters = [
            'statut' => $request->query->get('statut'),
            'urgence' => $request->query->get('urgence'),
            'nature' => $request->query->get('nature'),
            'search' => $request->query->get('search'),
        ];

        // Récupérer toutes les demandes sans filtres côté serveur
        $demandes = $demandeRepository->findByFilters([], $this->getUser());

        // Récupérer la liste des gestionnaires (techniciens)
        $techniciens = $userRepository->findByAdvancedRoles();

        if ($request->headers->get('X-Requested-With') === 'XMLHttpRequest') {
            if ($request->query->get('format') === 'json') {
                // Préparer les données pour JSON
                $demandesData = [];
                foreach ($demandes as $demande) {
                    $items = [];
                    foreach ($demande->getItems() as $item) {
                        $items[] = [
                            'id' => $item->getId(),
                            'reference' => $item->getReference(),
                            'quantite' => $item->getQuantite()
                        ];
                    }

                    $demandesData[] = [
                        'id' => $demande->getId(),
                        'dateCreation' => $demande->getDateCreation()->format('d/m/Y'),
                        'dateSouhaitee' => $demande->getDateSouhaitee()->format('d/m/Y'),
                        'technicien' => $demande->getTechnicien() ? $demande->getTechnicien()->getNomComplet() : null,
                        'urgence' => $demande->getUrgence(),
                        'urgenceLabel' => $demande->getUrgenceLabel(),
                        'nature' => $demande->getNature(),
                        'natureLabel' => $demande->getNatureLabel(),
                        'destinataire' => $demande->getDestinataireComplet(),
                        'compte' => $demande->getCompte(),
                        'statut' => $demande->getStatut(),
                        'statutLabel' => $demande->getStatutLabel(),
                        'items' => $items,
                        'itemsCount' => count($items),
                        'commentaireDemandeur' => $demande->getCommentaireDemandeur(),
                        'commentaireGestionnaire' => $demande->getCommentaireGestionnaire(),
                        'showUrl' => $this->generateUrl('app_demande_show', ['id' => $demande->getId()]),
                        'editUrl' => $this->generateUrl('app_demande_edit', ['id' => $demande->getId()]),
                        'canEdit' => !in_array($demande->getStatut(), [Demande::STATUT_TRAITEE, Demande::STATUT_REFUSEE])
                    ];
                }

                return new JsonResponse($demandesData);
            } else {
                // Pour la compatibilité avec l'ancien système
                $filteredDemandes = $demandeRepository->findByFilters($filters, $this->getUser());
                return $this->render('demande/_list.html.twig', [
                    'demandes' => $filteredDemandes,
                ]);
            }
        }

        return $this->render('demande/index.html.twig', [
            'demandes' => $demandes,
            'filters' => $filters,
            'techniciens' => $techniciens,
        ]);
    }

    #[Route('/new', name: 'app_demande_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager, EmailService $emailService): Response
    {
        $demande = new Demande();

        // Ajouter un item par défaut
        $item = new DemandeItem();
        $demande->addItem($item);

        $form = $this->createForm(DemandeType::class, $demande);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $demande->setDemandeur($this->getUser());

            // Définir le statut en attente
            $demande->setStatut(Demande::STATUT_EN_ATTENTE);

            $entityManager->persist($demande);
            $entityManager->flush();

            // Envoyer un email de confirmation
            $emailService->sendDemandeCreatedEmail($demande);

            $this->addFlash('success', 'Demande créée avec succès.');

            return $this->redirectToRoute('app_demande_show', ['id' => $demande->getId()], Response::HTTP_SEE_OTHER);
        }

        return $this->render('demande/new.html.twig', [
            'demande' => $demande,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_demande_show', methods: ['GET'])]
    public function show(Demande $demande, UserRepository $userRepository): Response
    {
        // Vérifier que l'utilisateur est le propriétaire de la demande, un admin ou est le gestionnaire assigné
        $user = $this->getUser();
        if ($demande->getDemandeur() !== $user &&
            !$user->isAdmin() &&
            $demande->getTechnicien() !== $user) {
            throw $this->createAccessDeniedException('Vous n\'êtes pas autorisé à voir cette demande.');
        }

        // Récupérer les techniciens pour la modale de validation
        $techniciens = [];
        if ($user->isAdmin()) {
            // Récupérer uniquement les utilisateurs qui ont les rôles spécifiques
            $advancedRoles = [
                'ROLE_USER_APPROVISIONNEMENT',
                'ROLE_MANAGER_GAP',
                'ROLE_MANAGER_USINAGE',
                'ROLE_MANAGER_DIRECTION',
                'ROLE_MANAGER_SUPPLY_CHAIN',
                'ROLE_ADMIN'
            ];

            $techniciens = $userRepository->createQueryBuilder('u')
                ->where('u.roles LIKE :role1 OR u.roles LIKE :role2 OR u.roles LIKE :role3 OR u.roles LIKE :role4 OR u.roles LIKE :role5 OR u.roles LIKE :role6 OR u.username = :eerdmann')
                ->andWhere('u.username != :excluded_user')
                ->setParameter('role1', '%"' . $advancedRoles[0] . '"%')
                ->setParameter('role2', '%"' . $advancedRoles[1] . '"%')
                ->setParameter('role3', '%"' . $advancedRoles[2] . '"%')
                ->setParameter('role4', '%"' . $advancedRoles[3] . '"%')
                ->setParameter('role5', '%"' . $advancedRoles[4] . '"%')
                ->setParameter('role6', '%"' . $advancedRoles[5] . '"%')
                ->setParameter('eerdmann', 'eerdmann')
                ->setParameter('excluded_user', 'fkleindienst')
                ->orderBy('u.nom', 'ASC')
                ->addOrderBy('u.prenom', 'ASC')
                ->getQuery()
                ->getResult();
        }

        return $this->render('demande/show.html.twig', [
            'demande' => $demande,
            'techniciens' => $techniciens,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_demande_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Demande $demande, EntityManagerInterface $entityManager): Response
    {
        // Vérifier que l'utilisateur est le propriétaire de la demande ou un admin
        $user = $this->getUser();
        if ($demande->getDemandeur() !== $user && !$user->isAdmin()) {
            throw $this->createAccessDeniedException('Vous n\'êtes pas autorisé à modifier cette demande.');
        }

        // Vérifier que la demande n'est pas déjà traitée ou refusée
        if (in_array($demande->getStatut(), [Demande::STATUT_TRAITEE, Demande::STATUT_REFUSEE])) {
            $this->addFlash('error', 'Modification impossible : demande traitée ou refusée.');
            return $this->redirectToRoute('app_demande_show', ['id' => $demande->getId()]);
        }

        $form = $this->createForm(DemandeType::class, $demande);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'Demande modifiée.');

            return $this->redirectToRoute('app_demande_show', ['id' => $demande->getId()], Response::HTTP_SEE_OTHER);
        }

        return $this->render('demande/edit.html.twig', [
            'demande' => $demande,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_demande_delete', methods: ['POST'])]
    public function delete(Request $request, Demande $demande, EntityManagerInterface $entityManager): Response
    {
        // Vérifier que l'utilisateur est le propriétaire de la demande ou un admin
        $user = $this->getUser();
        if ($demande->getDemandeur() !== $user && !$user->isAdmin()) {
            throw $this->createAccessDeniedException('Vous n\'êtes pas autorisé à supprimer cette demande.');
        }

        // Vérifier que la demande n'est pas déjà traitée ou validée
        if (in_array($demande->getStatut(), [Demande::STATUT_TRAITEE, Demande::STATUT_VALIDEE])) {
            $this->addFlash('error', 'Suppression impossible : demande traitée ou validée.');
            return $this->redirectToRoute('app_demande_show', ['id' => $demande->getId()]);
        }

        if ($this->isCsrfTokenValid('delete'.$demande->getId(), $request->request->get('_token'))) {
            $entityManager->remove($demande);
            $entityManager->flush();
            $this->addFlash('success', 'Demande supprimée.');
        }

        return $this->redirectToRoute('app_demande_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/{id}/update-status', name: 'app_demande_update_status', methods: ['POST'])]
    public function updateStatus(Request $request, Demande $demande, EntityManagerInterface $entityManager, EmailService $emailService): Response
    {
        // Vérifier que l'utilisateur est un administrateur ou le gestionnaire assigné
        $user = $this->getUser();
        if (!$user->isAdmin() && ($demande->getTechnicien() !== $user || $demande->getStatut() !== Demande::STATUT_VALIDEE)) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour accéder à cette page.');
        }

        // Vérifier le token CSRF
        $updateType = $request->request->get('update_type');
        $tokenId = ($updateType === 'gestionnaire') ? 'update_gestionnaire_' . $demande->getId() : 'update_admin_' . $demande->getId();
        $submittedToken = $request->request->get('_token');


        // Récupérer les données du formulaire
        $statut = $request->request->get('statut');
        $commentaire = $request->request->get('commentaire');
        $wbs = $request->request->get('wbs');
        $dateGestionnaire = $request->request->get('dateGestionnaire');
        $numeroOF = $request->request->get('numeroOF');

        // Valider le statut
        if (!in_array($statut, [Demande::STATUT_EN_ATTENTE, Demande::STATUT_VALIDEE, Demande::STATUT_REFUSEE, Demande::STATUT_TRAITEE, Demande::STATUT_CLOTUREE])) {
            if ($request->headers->get('X-Requested-With') === 'XMLHttpRequest') {
                return new JsonResponse(['success' => false, 'message' => 'Statut non valide.']);
            }

            $this->addFlash('error', 'Statut non valide.');
            return $this->redirectToRoute('app_demande_show', ['id' => $demande->getId()]);
        }

        // Sauvegarder l'ancien statut pour l'email et la détection de changement
        $oldStatus = $demande->getStatut();
        $statusChanged = ($oldStatus !== $statut);

        // Sauvegarder les anciennes valeurs pour détecter les changements
        $oldWbs = $demande->getWbs();
        $oldDateGestionnaire = $demande->getDateGestionnaire() ? $demande->getDateGestionnaire()->format('Y-m-d') : null;
        $oldNumeroOF = $demande->getNumeroOF();

        // Mettre à jour le statut
        $demande->setStatut($statut);

        // Mettre à jour le WBS
        $wbsChanged = false;
        if ($wbs !== null && $wbs !== $oldWbs) {
            $demande->setWbs($wbs);
            $wbsChanged = true;
        }

        // Mettre à jour la date gestionnaire
        $dateChanged = false;
        if ($dateGestionnaire !== null && $dateGestionnaire !== '') {
            $newDate = new \DateTime($dateGestionnaire);
            $newDateFormatted = $newDate->format('Y-m-d');

            if ($oldDateGestionnaire !== $newDateFormatted) {
                $demande->setDateGestionnaire($newDate);
                $dateChanged = true;
            }
        } elseif ($dateGestionnaire === '' && $oldDateGestionnaire !== null) {
            $demande->setDateGestionnaire(null);
            $dateChanged = true;
        }

        // Mettre à jour le numéro OF
        $numeroOFChanged = false;
        if ($numeroOF !== null && $numeroOF !== $oldNumeroOF) {
            $demande->setNumeroOF($numeroOF);
            $numeroOFChanged = true;
        }

        // Mettre à jour le commentaire si fourni
        $commentaireChanged = false;
        if ($commentaire !== null) {
            $oldCommentaire = $demande->getCommentaireGestionnaire();
            $updateType = $request->request->get('update_type');
            $editFullComment = $request->request->get('edit_full_comment') === 'true';

            // Si on édite le commentaire complet (mode admin)
            if ($editFullComment && $updateType === 'admin') {
                // Remplacer complètement le commentaire
                if (trim($commentaire) !== trim($oldCommentaire)) {
                    $demande->setCommentaireGestionnaire($commentaire);
                    $commentaireChanged = true;
                }
            }
            // Sinon, ajouter un nouveau commentaire à la suite
            else if (trim($commentaire) !== '') {
                // Si c'est une mise à jour par le gestionnaire assigné
                if ($updateType === 'gestionnaire') {
                    $newCommentaire = ($oldCommentaire ? $oldCommentaire . "\n\n" : '') .
                        "Commentaire ajouté par " . $this->getUser()->getNomComplet() . " le " . (new \DateTime())->format('d/m/Y H:i') . ":\n" .
                        $commentaire;
                } else {
                    // Sinon c'est une mise à jour de statut par un admin
                    $newCommentaire = ($oldCommentaire ? $oldCommentaire . "\n\n" : '') .
                        "Statut modifié par " . $this->getUser()->getNomComplet() . " le " . (new \DateTime())->format('d/m/Y H:i') . ":\n" .
                        $commentaire;
                }

                $demande->setCommentaireGestionnaire($newCommentaire);
                $commentaireChanged = true;
            }
        }

        // Sauvegarder les modifications
        $entityManager->flush();

        // Envoyer un email pour notifier du changement de statut (seulement si le statut a changé)
        if ($statusChanged) {
            $emailService->sendDemandeStatusChangedEmail($demande, $oldStatus);
        }

        // Répondre en fonction du type de requête
        if ($request->headers->get('X-Requested-With') === 'XMLHttpRequest') {
            // Préparer la réponse AJAX
            $response = [
                'success' => true,
                'statut' => $demande->getStatutLabel(),
                'commentaire' => $demande->getCommentaireGestionnaire(),
                'wbs' => $demande->getWbs(),
                'numeroOF' => $demande->getNumeroOF(),
                'dateGestionnaire' => $demande->getDateGestionnaire() ? $demande->getDateGestionnaire()->format('d/m/Y') : null,
                'id' => $demande->getId(),
                'oldStatus' => $oldStatus,
                'newStatus' => $demande->getStatut(),
                'statusChanged' => $statusChanged,
                'wbsChanged' => $wbsChanged,
                'dateChanged' => $dateChanged,
                'numeroOFChanged' => $numeroOFChanged,
                'commentaireChanged' => $commentaireChanged,
                'message' => 'Mise à jour effectuée'
            ];

            return new JsonResponse($response);
        }

        // Réponse pour les requêtes non-AJAX
        $this->addFlash('success', 'Statut mis à jour.');
        return $this->redirectToRoute('app_demande_show', ['id' => $demande->getId()]);
    }
}
