<?php

namespace App\Entity;

use App\Repository\DemandeItemRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: DemandeItemRepository::class)]
class DemandeItem
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank(message: "La référence est obligatoire")]
    private ?string $reference = null;

    #[ORM\Column]
    #[Assert\NotBlank(message: "La quantité est obligatoire")]
    #[Assert\Positive(message: "La quantité doit être positive")]
    private ?int $quantite = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    #[Assert\GreaterThanOrEqual(value: "today", message: "La date souhaitée doit être supérieure ou égale à aujourd'hui")]
    private ?\DateTimeInterface $dateSouhaitee = null;

    #[ORM\Column(length: 500, nullable: true)]
    private ?string $lienPlan = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $datePrevisionelle = null;

    #[ORM\ManyToOne(inversedBy: 'items')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Demande $demande = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getReference(): ?string
    {
        return $this->reference;
    }

    public function setReference(string $reference): static
    {
        $this->reference = $reference;

        return $this;
    }

    public function getQuantite(): ?int
    {
        return $this->quantite;
    }

    public function setQuantite(int $quantite): static
    {
        $this->quantite = $quantite;

        return $this;
    }

    public function getDemande(): ?Demande
    {
        return $this->demande;
    }

    public function setDemande(?Demande $demande): static
    {
        $this->demande = $demande;

        return $this;
    }

    public function getDateSouhaitee(): ?\DateTimeInterface
    {
        return $this->dateSouhaitee;
    }

    public function setDateSouhaitee(?\DateTimeInterface $dateSouhaitee): static
    {
        $this->dateSouhaitee = $dateSouhaitee;

        return $this;
    }

    public function getLienPlan(): ?string
    {
        return $this->lienPlan;
    }

    public function setLienPlan(?string $lienPlan): static
    {
        $this->lienPlan = $lienPlan;

        return $this;
    }

    public function getDatePrevisionelle(): ?\DateTimeInterface
    {
        return $this->datePrevisionelle;
    }

    public function setDatePrevisionelle(?\DateTimeInterface $datePrevisionelle): static
    {
        $this->datePrevisionelle = $datePrevisionelle;

        return $this;
    }
}
