{{ form_start(form, {'attr': {'class': 'space-y-8'}}) }}
    <!-- Section Informations gestionnaire -->
    <div class="form-section mb-6">
        <div class="form-section-header">
            <h3 class="text-lg font-medium text-gray-800">
                <i class="fas fa-user-cog mr-2 text-primary"></i>
                Informations gestionnaire
            </h3>
        </div>
        <div class="form-section-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    {{ form_label(form.wbs, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.wbs, {'attr': {'class': 'form-input'}}) }}
                    {{ form_errors(form.wbs, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    {{ form_label(form.numeroOF, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.numeroOF, {'attr': {'class': 'form-input'}}) }}
                    {{ form_errors(form.numeroOF, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>

                <div class="form-group">
                    {{ form_label(form.dateGestionnaire, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                    {{ form_widget(form.dateGestionnaire, {'attr': {'class': 'form-input'}}) }}
                    {{ form_errors(form.dateGestionnaire, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                </div>
            </div>
        </div>
    </div>

    <!-- Section Items avec dates prévisionnelles -->
    <div class="form-section mb-6">
        <div class="form-section-header">
            <h3 class="text-lg font-medium text-gray-800">
                <i class="fas fa-boxes mr-2 text-primary"></i>
                Items et dates prévisionnelles
            </h3>
            <p class="text-sm text-gray-500 mt-1">Définissez les dates prévisionnelles de réception ou de suivi pour chaque item</p>
        </div>
        <div class="form-section-body">
            <div class="space-y-4">
                {% for item in form.items %}
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            <div>
                                {{ form_label(item.reference, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                                {{ form_widget(item.reference, {'attr': {'class': 'form-input bg-gray-50'}}) }}
                                {{ form_errors(item.reference, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                            </div>
                            <div>
                                {{ form_label(item.quantite, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                                {{ form_widget(item.quantite, {'attr': {'class': 'form-input bg-gray-50'}}) }}
                                {{ form_errors(item.quantite, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                            </div>
                            <div>
                                {{ form_label(item.dateSouhaitee, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                                {{ form_widget(item.dateSouhaitee, {'attr': {'class': 'form-input bg-gray-50'}}) }}
                                {{ form_errors(item.dateSouhaitee, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                            </div>
                            <div>
                                {{ form_label(item.lienPlan, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                                {{ form_widget(item.lienPlan, {'attr': {'class': 'form-input bg-gray-50'}}) }}
                                {{ form_errors(item.lienPlan, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                            </div>
                            <div>
                                {{ form_label(item.datePrevisionelle, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                                {{ form_widget(item.datePrevisionelle, {'attr': {'class': 'form-input'}}) }}
                                {{ form_errors(item.datePrevisionelle, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Section Commentaire -->
    <div class="form-section mb-6">
        <div class="form-section-header">
            <h3 class="text-lg font-medium text-gray-800">
                <i class="fas fa-comment mr-2 text-primary"></i>
                Commentaire du gestionnaire
            </h3>
        </div>
        <div class="form-section-body">
            <div class="form-group">
                {{ form_label(form.commentaireGestionnaire, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700 mb-1'}}) }}
                {{ form_widget(form.commentaireGestionnaire, {'attr': {'class': 'form-textarea w-full', 'rows': 4}}) }}
                {{ form_errors(form.commentaireGestionnaire, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
            </div>
        </div>
    </div>

    <!-- Boutons d'action -->
    <div class="pt-4 border-t border-gray-200">
        <div class="flex justify-end space-x-3">
            <a href="{{ path('app_demande_show', {'id': form.vars.data.id}) }}" class="btn btn-secondary">
                Annuler
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save mr-2"></i>
                {{ button_label|default('Enregistrer') }}
            </button>
        </div>
    </div>
{{ form_end(form) }}
