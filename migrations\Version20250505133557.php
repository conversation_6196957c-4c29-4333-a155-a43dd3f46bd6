<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250505133557 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE service (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, UNIQUE INDEX UNIQ_E19D9AD25E237E06 (name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user ADD username VARCHAR(255) DEFAULT NULL, ADD secteur VARCHAR(255) DEFAULT NULL, ADD manager VARCHAR(255) DEFAULT NULL, ADD is_manager TINYINT(1) DEFAULT NULL, ADD titre VARCHAR(255) DEFAULT NULL, ADD vpn TINYINT(1) DEFAULT NULL, ADD mobile VARCHAR(255) DEFAULT NULL, ADD telephone_number VARCHAR(255) DEFAULT NULL, CHANGE password password VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_8D93D649F85E0677 ON user (username)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE service
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX UNIQ_8D93D649F85E0677 ON user
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user DROP username, DROP secteur, DROP manager, DROP is_manager, DROP titre, DROP vpn, DROP mobile, DROP telephone_number, CHANGE password password VARCHAR(255) NOT NULL
        SQL);
    }
}
