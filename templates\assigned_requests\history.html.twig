{% extends 'base.html.twig' %}

{% block title %}Historique des demandes traitées{% endblock %}

{% block body %}
<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold text-gray-900">Historique des demandes traitées</h1>
        <a href="{{ path('app_assigned_requests_index') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            Retour aux demandes à traiter
        </a>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        {% if demandes|length > 0 %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de création</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date souhaitée</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Demandeur</th>
                            {% if isAdmin %}
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gestionnaire</th>
                            {% endif %}
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nature</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Urgence</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for demande in demandes %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ demande.id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ demande.dateCreation|date('d/m/Y') }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ demande.dateSouhaitee|date('d/m/Y') }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ demande.demandeur.nomComplet }}</td>
                                {% if isAdmin %}
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ demande.technicien ? demande.technicien.nomComplet : 'Non assigné' }}</td>
                                {% endif %}
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ demande.natureLabel }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        {% if demande.urgence == 'haute' %}
                                            bg-red-100 text-red-800
                                        {% elseif demande.urgence == 'moyenne' %}
                                            bg-yellow-100 text-yellow-800
                                        {% else %}
                                            bg-green-100 text-green-800
                                        {% endif %}
                                    ">
                                        {{ demande.urgenceLabel }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ demande.items|length }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{ path('app_demande_show', {'id': demande.id}) }}" class="text-primary hover:text-blue-900">Voir</a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="px-6 py-4 text-center text-gray-500">
                Aucune demande traitée trouvée.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
