<?php
// add_columns.php
require_once 'vendor/autoload.php';

use Symfony\Component\Dotenv\Dotenv;

// Charger les variables d'environnement
$dotenv = new Dotenv();
$dotenv->loadEnv(__DIR__.'/.env');

// Configurer la connexion à la base de données
$dbParams = [
    'driver'   => 'pdo_mysql',
    'host'     => $_ENV['DATABASE_HOST'] ?? 'localhost',
    'user'     => $_ENV['DATABASE_USER'] ?? 'root',
    'password' => $_ENV['DATABASE_PASSWORD'] ?? '',
    'dbname'   => $_ENV['DATABASE_NAME'] ?? 'demande_track',
    'charset'  => 'utf8mb4',
];

// Créer la connexion
$conn = \Doctrine\DBAL\DriverManager::getConnection($dbParams);

// Vérifier si les colonnes existent déjà
$schemaManager = $conn->createSchemaManager();
$columns = $schemaManager->listTableColumns('demande');

$hasWbs = false;
$hasDateGestionnaire = false;

foreach ($columns as $column) {
    if ($column->getName() === 'wbs') {
        $hasWbs = true;
    }
    if ($column->getName() === 'date_gestionnaire') {
        $hasDateGestionnaire = true;
    }
}

// Ajouter les colonnes si elles n'existent pas
if (!$hasWbs) {
    echo "Ajout de la colonne 'wbs'...\n";
    $conn->executeStatement('ALTER TABLE demande ADD wbs VARCHAR(255) DEFAULT NULL');
    echo "Colonne 'wbs' ajoutée avec succès.\n";
} else {
    echo "La colonne 'wbs' existe déjà.\n";
}

if (!$hasDateGestionnaire) {
    echo "Ajout de la colonne 'date_gestionnaire'...\n";
    $conn->executeStatement('ALTER TABLE demande ADD date_gestionnaire DATE DEFAULT NULL');
    echo "Colonne 'date_gestionnaire' ajoutée avec succès.\n";
} else {
    echo "La colonne 'date_gestionnaire' existe déjà.\n";
}

echo "Opération terminée.\n";
