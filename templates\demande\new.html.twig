{% extends 'base.html.twig' %}

{% block title %}Nouvelle demande{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Styles pour les sections du formulaire */
        .form-section {
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .form-section:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .form-section-header {
            background-color: #F9FAFB;
            border-bottom: 1px solid #E5E7EB;
            padding: 1rem 1.5rem;
        }

        .form-section-body {
            padding: 1.5rem;
        }

        /* Styles pour les champs de formulaire */
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #D1D5DB;
            border-radius: 0.375rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #3B82F6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
            outline: none;
        }

        /* Styles pour les items */
        .item-row {
            transition: all 0.2s ease;
            background-color: #F9FAFB;
        }

        .item-row:hover {
            background-color: #F3F4F6;
            transform: translateY(-1px);
        }

        /* Styles pour les boutons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            font-weight: 500;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn-primary {
            background-color: #3B82F6;
            color: white;
            border: 1px solid transparent;
        }

        .btn-primary:hover {
            background-color: #2563EB;
            box-shadow: 0 4px 6px rgba(59, 130, 246, 0.2);
        }

        .btn-secondary {
            background-color: white;
            color: #4B5563;
            border: 1px solid #D1D5DB;
        }

        .btn-secondary:hover {
            background-color: #F9FAFB;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .btn-success {
            background-color: #10B981;
            color: white;
            border: 1px solid transparent;
        }

        .btn-success:hover {
            background-color: #059669;
            box-shadow: 0 4px 6px rgba(16, 185, 129, 0.2);
        }

        .btn-danger {
            background-color: #EF4444;
            color: white;
            border: 1px solid transparent;
        }

        .btn-danger:hover {
            background-color: #DC2626;
            box-shadow: 0 4px 6px rgba(239, 68, 68, 0.2);
        }
    </style>
{% endblock %}

{% block body %}
<div class="max-w-7xl mx-auto space-y-6 py-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Nouvelle demande</h1>
            <p class="mt-1 text-sm text-gray-600">Créez une nouvelle demande en remplissant le formulaire ci-dessous</p>
        </div>
        <a href="{{ path('app_demande_index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>
            Retour à la liste
        </a>
    </div>

    <div class="form-section">
        <div class="form-section-body">
            {{ include('demande/_form.html.twig') }}
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Collection handling for items
            const collectionHolder = document.querySelector('[data-prototype]');
            const itemsContainer = document.getElementById('items-container');
            const addItemButton = document.getElementById('add-item');

            // Count the initial items
            let index = document.querySelectorAll('.item-row').length;

            // Add a new item
            addItemButton.addEventListener('click', function(e) {
                e.preventDefault();
                addItemForm();
            });

            function addItemForm() {
                // Get the prototype
                const prototype = collectionHolder.dataset.prototype;

                // Replace __name__ with the current index
                const newForm = prototype.replace(/__name__/g, index);

                // Create a temporary div to parse the form
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = newForm;

                // Create the main item div
                const itemDiv = document.createElement('div');
                itemDiv.classList.add('item-row', 'flex', 'items-start', 'space-x-3', 'mb-3', 'p-4', 'border', 'border-gray-200', 'rounded-lg');

                // Create reference div
                const referenceDiv = document.createElement('div');
                referenceDiv.classList.add('flex-1', 'form-group');

                // Find reference label, input and errors
                const referenceLabel = tempDiv.querySelector('label[for$="_reference"]');
                const referenceInput = tempDiv.querySelector('input[id$="_reference"]');
                const referenceErrors = tempDiv.querySelector('div:has(input[id$="_reference"]) + div.invalid-feedback');

                if (referenceLabel) {
                    referenceLabel.classList.add('block', 'text-sm', 'font-medium', 'text-gray-700', 'mb-1');
                    referenceDiv.appendChild(referenceLabel);
                }
                if (referenceInput) {
                    referenceInput.classList.add('form-input');
                    referenceDiv.appendChild(referenceInput);
                }
                if (referenceErrors) referenceDiv.appendChild(referenceErrors);

                // Create quantity div
                const quantiteDiv = document.createElement('div');
                quantiteDiv.classList.add('w-32', 'form-group');

                // Find quantity label, input and errors
                const quantiteLabel = tempDiv.querySelector('label[for$="_quantite"]');
                const quantiteInput = tempDiv.querySelector('input[id$="_quantite"]');
                const quantiteErrors = tempDiv.querySelector('div:has(input[id$="_quantite"]) + div.invalid-feedback');

                if (quantiteLabel) {
                    quantiteLabel.classList.add('block', 'text-sm', 'font-medium', 'text-gray-700', 'mb-1');
                    quantiteDiv.appendChild(quantiteLabel);
                }
                if (quantiteInput) {
                    quantiteInput.classList.add('form-input');
                    quantiteDiv.appendChild(quantiteInput);
                }
                if (quantiteErrors) quantiteDiv.appendChild(quantiteErrors);

                // Add the divs to the item row
                itemDiv.appendChild(referenceDiv);
                itemDiv.appendChild(quantiteDiv);

                // Add delete button
                const deleteButton = document.createElement('button');
                deleteButton.type = 'button';
                deleteButton.classList.add('btn', 'btn-danger', 'p-2', 'mt-7');
                deleteButton.innerHTML = '<i class="fas fa-trash-alt"></i>';
                deleteButton.addEventListener('click', function() {
                    itemDiv.remove();
                });

                itemDiv.appendChild(deleteButton);

                // Add the form to the container
                itemsContainer.appendChild(itemDiv);

                // Increment the index
                index++;
            }

            // Add delete buttons to existing items
            document.querySelectorAll('.item-row').forEach(function(item) {
                const deleteButton = document.createElement('button');
                deleteButton.type = 'button';
                deleteButton.classList.add('btn', 'btn-danger', 'p-2', 'mt-7');
                deleteButton.innerHTML = '<i class="fas fa-trash-alt"></i>';
                deleteButton.addEventListener('click', function() {
                    item.remove();
                });

                item.appendChild(deleteButton);
            });

            // Le champ destinataire (secteur) est maintenant un simple champ texte

            // Gestion du datalist pour le destinataire utilisateur
            const destinataireUserInput = document.getElementById('destinataire-user-input');
            const destinataireUserHidden = document.getElementById('destinataire-user-hidden');
            const destinataireUserSelect = document.querySelector('select[name="{{ form.destinataireUser.vars.full_name }}"]');
            const datalistOptions = document.querySelectorAll('#destinataire-user-list option');

            // Créer un mapping des noms vers les IDs
            const userMap = {};
            datalistOptions.forEach(option => {
                userMap[option.value] = option.getAttribute('data-id');
            });

            // Mettre à jour le champ caché et le select quand l'utilisateur sélectionne un destinataire
            destinataireUserInput.addEventListener('input', function() {
                const selectedName = this.value;
                const selectedId = userMap[selectedName];

                if (selectedId) {
                    destinataireUserHidden.value = selectedId;
                    // Mettre à jour le select caché
                    if (destinataireUserSelect) {
                        destinataireUserSelect.value = selectedId;
                    }
                } else {
                    destinataireUserHidden.value = '';
                    if (destinataireUserSelect) {
                        destinataireUserSelect.value = '';
                    }
                }
            });

            // Si une valeur est déjà sélectionnée, afficher le nom correspondant
            if (destinataireUserHidden.value) {
                for (const [name, id] of Object.entries(userMap)) {
                    if (id === destinataireUserHidden.value) {
                        destinataireUserInput.value = name;
                        break;
                    }
                }
            }
        });
    </script>
{% endblock %}
