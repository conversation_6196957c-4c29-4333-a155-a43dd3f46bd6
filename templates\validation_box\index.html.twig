{% extends 'base.html.twig' %}

{% block title %}Boîte de validation{% endblock %}

{% block body %}
<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold text-gray-900">Boîte de validation</h1>
        <a href="{{ path('app_demande_index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i> Retour à la liste
        </a>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Demandes en attente de validation</h3>
            <p class="mt-1 text-sm text-gray-500"><PERSON><PERSON><PERSON> ou refusez les demandes d'achat</p>
        </div>

        {% if demandes|length > 0 %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Demandeur</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nature</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Urgence</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for demande in demandes %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ demande.dateCreation|date('d/m/Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ demande.demandeur.nomComplet }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ demande.natureLabel }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        {% if demande.urgence == 'haute' %}
                                            bg-red-100 text-red-800
                                        {% elseif demande.urgence == 'moyenne' %}
                                            bg-yellow-100 text-yellow-800
                                        {% else %}
                                            bg-green-100 text-green-800
                                        {% endif %}
                                    ">
                                        {{ demande.urgenceLabel }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ demande.items|length }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-3">
                                        <a href="{{ path('app_demande_show', {'id': demande.id}) }}" class="inline-flex items-center p-1.5 text-sm text-gray-600 hover:text-primary transition-colors duration-200" title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="inline-flex items-center p-1.5 text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200" onclick="openValidateModal({{ demande.id }})" title="Valider la demande">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="inline-flex items-center p-1.5 text-sm text-gray-600 hover:text-red-600 transition-colors duration-200" onclick="openRejectModal({{ demande.id }})" title="Refuser la demande">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="px-6 py-4 text-center text-gray-500">
                Aucune demande en attente de validation.
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal de validation -->
<div id="validate-modal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-middle bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full animate__animated animate__fadeInDown animate__faster">
            <form id="validate-form" method="post" action="">
                <input type="hidden" name="_token" id="validate-token" value="">
                <div class="bg-white px-6 pt-6 pb-6">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mr-4">
                            <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2" id="modal-title">
                                Valider la demande
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Êtes-vous sûr de vouloir valider cette demande ? Veuillez assigner un gestionnaire et ajouter un commentaire si nécessaire.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="bg-blue-50 p-4 rounded-md border border-blue-100">
                            <label for="technicien-input" class="block text-sm font-medium text-blue-800 mb-2">Assigner à un gestionnaire <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-gray-400"></i>
                                </div>
                                <input type="text" id="technicien-input" list="technicien-list" placeholder="Rechercher un gestionnaire..."
                                    class="pl-10 block w-full py-3 px-4 border border-blue-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-base" required>
                            </div>
                            <datalist id="technicien-list">
                                {% for technicien in techniciens %}
                                    <option value="{{ technicien.nomComplet }}" data-id="{{ technicien.id }}">
                                {% endfor %}
                            </datalist>
                            <input type="hidden" id="technicien" name="technicien" value="">
                        </div>

                        <div>
                            <label for="validate-commentaire" class="block text-sm font-medium text-gray-700 mb-2">Commentaire (optionnel)</label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment"></i>
                                </div>
                                <textarea id="validate-commentaire" name="commentaire" rows="4"
                                    class="pl-10 block w-full py-3 px-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-base"
                                    placeholder="Ajoutez un commentaire concernant cette validation..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 px-6 py-4 flex flex-col sm:flex-row-reverse sm:justify-end gap-3">
                    <button type="submit" class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-3 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto transition-colors duration-200">
                        <i class="fas fa-check mr-2"></i> Valider
                    </button>
                    <button type="button" class="w-full inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto transition-colors duration-200" onclick="closeModal('validate-modal')">
                        <i class="fas fa-times mr-2"></i> Annuler
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de refus -->
<div id="reject-modal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-middle bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full animate__animated animate__fadeInDown animate__faster">
            <form id="reject-form" method="post" action="">
                <input type="hidden" name="_token" id="reject-token" value="">
                <div class="bg-white px-6 pt-6 pb-6">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mr-4">
                            <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2" id="modal-title">
                                Refuser la demande
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Êtes-vous sûr de vouloir refuser cette demande ? Veuillez indiquer la raison du refus ci-dessous.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="bg-red-50 p-4 rounded-md border border-red-100">
                            <label for="reject-commentaire" class="block text-sm font-medium text-red-800 mb-2">Raison du refus <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment-slash"></i>
                                </div>
                                <textarea id="reject-commentaire" name="commentaire" rows="5"
                                    class="pl-10 block w-full py-3 px-4 border border-red-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-base"
                                    placeholder="Veuillez expliquer pourquoi cette demande est refusée..." required></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 px-6 py-4 flex flex-col sm:flex-row-reverse sm:justify-end gap-3">
                    <button type="submit" class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-3 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:w-auto transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i> Refuser
                    </button>
                    <button type="button" class="w-full inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto transition-colors duration-200" onclick="closeModal('reject-modal')">
                        <i class="fas fa-arrow-left mr-2"></i> Annuler
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <style>
        /* Styles pour les modales */
        .modal-backdrop {
            backdrop-filter: blur(2px);
        }

        /* Amélioration des champs de formulaire */
        textarea:focus, input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        /* Animation pour les boutons */
        .btn-hover-effect {
            transition: all 0.2s ease-in-out;
        }

        .btn-hover-effect:hover {
            transform: translateY(-2px);
        }

        /* Styles pour les textarea */
        textarea {
            min-height: 100px;
            resize: vertical;
        }

        /* Amélioration de la visibilité des champs obligatoires */
        .required-field {
            position: relative;
        }

        .required-field::after {
            content: '*';
            color: #ef4444;
            position: absolute;
            top: 0;
            right: -10px;
        }

        /* Animation pour les modales */
        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-animation {
            animation: modalFadeIn 0.3s ease-out forwards;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Fonction pour ouvrir la modale de validation
        function openValidateModal(id) {
            const modal = document.getElementById('validate-modal');
            const form = document.getElementById('validate-form');
            const token = document.getElementById('validate-token');
            const overlay = modal.querySelector('.fixed.inset-0');
            const modalContent = modal.querySelector('.inline-block');

            // Configurer le formulaire
            form.action = "{{ path('app_validation_box_validate', {'id': 'ID'}) }}".replace('ID', id);
            token.value = "{{ csrf_token('validate_demande') }}";

            // Afficher la modale avec animation
            modal.classList.remove('hidden');

            // Focus sur le champ de gestionnaire après l'animation
            setTimeout(() => {
                document.getElementById('technicien-input').focus();
            }, 300);

            // Ajouter un gestionnaire d'événements pour fermer la modale en cliquant sur l'overlay
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeModal('validate-modal');
                }
            });

            // Ajouter un gestionnaire d'événements pour la touche Echap
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                    closeModal('validate-modal');
                }
            });
        }

        // Fonction pour ouvrir la modale de refus
        function openRejectModal(id) {
            const modal = document.getElementById('reject-modal');
            const form = document.getElementById('reject-form');
            const token = document.getElementById('reject-token');
            const overlay = modal.querySelector('.fixed.inset-0');
            const modalContent = modal.querySelector('.inline-block');

            // Configurer le formulaire
            form.action = "{{ path('app_validation_box_reject', {'id': 'ID'}) }}".replace('ID', id);
            token.value = "{{ csrf_token('reject_demande') }}";

            // Afficher la modale avec animation
            modal.classList.remove('hidden');

            // Focus sur le champ de commentaire après l'animation
            setTimeout(() => {
                document.getElementById('reject-commentaire').focus();
            }, 300);

            // Ajouter un gestionnaire d'événements pour fermer la modale en cliquant sur l'overlay
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeModal('reject-modal');
                }
            });

            // Ajouter un gestionnaire d'événements pour la touche Echap
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                    closeModal('reject-modal');
                }
            });
        }

        // Fonction pour fermer la modale
        function closeModal(id) {
            const modal = document.getElementById(id);

            // Ajouter une classe pour l'animation de sortie
            modal.querySelector('.inline-block').classList.add('animate__fadeOutUp');

            // Attendre la fin de l'animation avant de cacher la modale
            setTimeout(() => {
                modal.classList.add('hidden');
                modal.querySelector('.inline-block').classList.remove('animate__fadeOutUp');

                // Réinitialiser les formulaires
                if (id === 'validate-modal') {
                    document.getElementById('technicien-input').value = '';
                    document.getElementById('technicien').value = '';
                    document.getElementById('validate-commentaire').value = '';
                } else if (id === 'reject-modal') {
                    document.getElementById('reject-commentaire').value = '';
                }
            }, 200);
        }

        // Fonction pour gérer la sélection du gestionnaire
        document.addEventListener('DOMContentLoaded', function() {
            const technicienInput = document.getElementById('technicien-input');
            const technicienHidden = document.getElementById('technicien');
            const datalistOptions = document.querySelectorAll('#technicien-list option');

            // Créer un mapping des noms vers les IDs
            const technicienMap = {};
            datalistOptions.forEach(option => {
                technicienMap[option.value] = option.getAttribute('data-id');
            });

            // Mettre à jour le champ caché quand l'utilisateur sélectionne un gestionnaire
            technicienInput.addEventListener('input', function() {
                const selectedName = this.value;
                const selectedId = technicienMap[selectedName];

                if (selectedId) {
                    technicienHidden.value = selectedId;
                    // Ajouter une classe pour indiquer que la sélection est valide
                    this.classList.add('border-green-300', 'bg-green-50');
                    this.classList.remove('border-red-300', 'bg-red-50');
                } else {
                    technicienHidden.value = '';
                    // Si la valeur n'est pas vide mais ne correspond à aucun gestionnaire
                    if (selectedName) {
                        this.classList.add('border-red-300', 'bg-red-50');
                        this.classList.remove('border-green-300', 'bg-green-50');
                    } else {
                        this.classList.remove('border-green-300', 'bg-green-50', 'border-red-300', 'bg-red-50');
                    }
                }
            });

            // Ajouter des classes btn-hover-effect aux boutons
            document.querySelectorAll('button[type="submit"], button[onclick^="closeModal"]').forEach(button => {
                button.classList.add('btn-hover-effect');
            });
        });
    </script>
{% endblock %}
