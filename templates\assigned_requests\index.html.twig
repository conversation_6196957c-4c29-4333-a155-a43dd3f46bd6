{% extends 'base.html.twig' %}

{% block title %}Demandes assignées{% endblock %}

{% block body %}
<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold text-gray-900">Demandes assignées</h1>
        <div class="flex space-x-2">
            <a href="{{ path('app_assigned_requests_history') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                Voir l'historique
            </a>
            <a href="{{ path('app_demande_index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Retour à la liste
            </a>
        </div>
    </div>

    <!-- Section Filtres -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filtres</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="filter-statut" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                <select id="filter-statut" class="form-select">
                    <option value="">Tous les statuts</option>
                    <option value="validee">Validée</option>
                    <option value="traitee">Traitée</option>
                </select>
            </div>
            <div>
                <label for="filter-urgence" class="block text-sm font-medium text-gray-700 mb-1">Urgence</label>
                <select id="filter-urgence" class="form-select">
                    <option value="">Toutes les urgences</option>
                    <option value="basse">Basse</option>
                    <option value="moyenne">Moyenne</option>
                    <option value="haute">Haute</option>
                </select>
            </div>
            <div>
                <label for="filter-nature" class="block text-sm font-medium text-gray-700 mb-1">Nature</label>
                <select id="filter-nature" class="form-select">
                    <option value="">Toutes les natures</option>
                    <option value="echantillons">Échantillons</option>
                    <option value="outillage">Outillage</option>
                    <option value="pieces">Pièces</option>
                </select>
            </div>
            <div>
                <label for="filter-search" class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
                <input type="text" id="filter-search" placeholder="ID, demandeur, référence..." class="form-input">
            </div>
        </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        {% if demandes|length > 0 %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de création</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date souhaitée</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Demandeur</th>
                            {% if isAdmin %}
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gestionnaire</th>
                            {% endif %}
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nature</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Urgence</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for demande in demandes %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ demande.id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ demande.dateCreation|date('d/m/Y') }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ demande.dateSouhaitee|date('d/m/Y') }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ demande.demandeur.nomComplet }}</td>
                                {% if isAdmin %}
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ demande.technicien ? demande.technicien.nomComplet : 'Non assigné' }}</td>
                                {% endif %}
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ demande.natureLabel }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        {% if demande.urgence == 'haute' %}
                                            bg-red-100 text-red-800
                                        {% elseif demande.urgence == 'moyenne' %}
                                            bg-yellow-100 text-yellow-800
                                        {% else %}
                                            bg-green-100 text-green-800
                                        {% endif %}
                                    ">
                                        {{ demande.urgenceLabel }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ demande.items|length }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        {% if demande.statut == 'validee' %}
                                            bg-blue-100 text-blue-800
                                        {% elseif demande.statut == 'traitee' %}
                                            bg-green-100 text-green-800
                                        {% endif %}">
                                        {{ demande.statutLabel }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-3">
                                        <a href="{{ path('app_demande_show', {'id': demande.id}) }}" class="inline-flex items-center p-1.5 text-sm text-gray-600 hover:text-primary transition-colors duration-200" title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if demande.statut == 'validee' %}
                                            <button type="button" class="inline-flex items-center p-1.5 text-sm text-gray-600 hover:text-green-600 transition-colors duration-200" onclick="openProcessModal({{ demande.id }})" title="Traiter la demande">
                                                <i class="fas fa-check-circle"></i>
                                            </button>
                                        {% elseif demande.statut == 'traitee' %}
                                            <button type="button" class="inline-flex items-center p-1.5 text-sm text-gray-600 hover:text-purple-600 transition-colors duration-200" onclick="openCloseModal({{ demande.id }})" title="Clôturer la demande">
                                                <i class="fas fa-archive"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="px-6 py-4 text-center text-gray-500">
                Aucune demande assignée trouvée.
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal de traitement -->
<div id="process-modal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity modal-backdrop" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-middle bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full animate__animated animate__faster">
            <form id="process-form" method="post" action="">
                <input type="hidden" name="_token" id="process-token" value="">
                <div class="bg-white px-6 pt-6 pb-6">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mr-4">
                            <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2" id="modal-title">
                                Marquer la demande comme traitée
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Êtes-vous sûr de vouloir marquer cette demande comme traitée ? Cette action est irréversible.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="bg-green-50 p-4 rounded-md border border-green-100">
                            <label for="commentaire" class="block text-sm font-medium text-green-800 mb-2">Commentaire (optionnel)</label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment"></i>
                                </div>
                                <textarea id="commentaire" name="commentaire" rows="4"
                                    class="pl-10 block w-full py-3 px-4 border border-green-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-base"
                                    placeholder="Ajoutez un commentaire concernant ce traitement..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 px-6 py-4 flex flex-col sm:flex-row-reverse sm:justify-end gap-3">
                    <button type="submit" class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-3 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:w-auto transition-colors duration-200 btn-hover-effect">
                        <i class="fas fa-check mr-2"></i> Confirmer
                    </button>
                    <button type="button" class="w-full inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto transition-colors duration-200 btn-hover-effect" onclick="closeProcessModal()">
                        <i class="fas fa-times mr-2"></i> Annuler
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de clôture -->
<div id="close-modal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity modal-backdrop" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-middle bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full animate__animated animate__faster">
            <form id="close-form" method="post" action="">
                <input type="hidden" name="_token" id="close-token" value="">
                <input type="hidden" name="statut" value="cloturee">
                <input type="hidden" name="update_type" value="gestionnaire">
                <div class="bg-white px-6 pt-6 pb-6">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 mr-4">
                            <svg class="h-6 w-6 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2" id="modal-title">
                                Clôturer la demande
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Êtes-vous sûr de vouloir clôturer cette demande ? Cette action finalise le traitement de la demande.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="bg-purple-50 p-4 rounded-md border border-purple-100">
                            <label for="close-commentaire" class="block text-sm font-medium text-purple-800 mb-2">Commentaire de clôture (optionnel)</label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment-dots"></i>
                                </div>
                                <textarea id="close-commentaire" name="commentaire" rows="4"
                                    class="pl-10 block w-full py-3 px-4 border border-purple-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-base"
                                    placeholder="Ajoutez un commentaire concernant la clôture de cette demande..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 px-6 py-4 flex flex-col sm:flex-row-reverse sm:justify-end gap-3">
                    <button type="submit" class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-3 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:w-auto transition-colors duration-200 btn-hover-effect">
                        <i class="fas fa-archive mr-2"></i> Clôturer
                    </button>
                    <button type="button" class="w-full inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto transition-colors duration-200 btn-hover-effect" onclick="closeCloseModal()">
                        <i class="fas fa-times mr-2"></i> Annuler
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterStatut = document.getElementById('filter-statut');
    const filterUrgence = document.getElementById('filter-urgence');
    const filterNature = document.getElementById('filter-nature');
    const filterSearch = document.getElementById('filter-search');
    const table = document.querySelector('table tbody');

    function filterTable() {
        const statutValue = filterStatut.value.toLowerCase();
        const urgenceValue = filterUrgence.value.toLowerCase();
        const natureValue = filterNature.value.toLowerCase();
        const searchValue = filterSearch.value.toLowerCase();

        const rows = table.querySelectorAll('tr');

        rows.forEach(row => {
            const statut = row.querySelector('td:nth-child(6)').textContent.toLowerCase();
            const urgence = row.querySelector('td:nth-child(5)').textContent.toLowerCase();
            const nature = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
            const searchableText = row.textContent.toLowerCase();

            const matchesStatut = !statutValue || statut.includes(statutValue);
            const matchesUrgence = !urgenceValue || urgence.includes(urgenceValue);
            const matchesNature = !natureValue || nature.includes(natureValue);
            const matchesSearch = !searchValue || searchableText.includes(searchValue);

            if (matchesStatut && matchesUrgence && matchesNature && matchesSearch) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    filterStatut.addEventListener('change', filterTable);
    filterUrgence.addEventListener('change', filterTable);
    filterNature.addEventListener('change', filterTable);
    filterSearch.addEventListener('input', filterTable);
});
</script>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <style>
        /* Animation pour les boutons */
        .btn-hover-effect {
            transition: all 0.2s ease-in-out;
        }

        .btn-hover-effect:hover {
            transform: translateY(-2px);
        }

        /* Styles pour les modales */
        .modal-backdrop {
            backdrop-filter: blur(2px);
        }

        /* Amélioration des champs de formulaire */
        textarea:focus, input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        /* Styles pour les textarea */
        textarea {
            min-height: 100px;
            resize: vertical;
        }

        /* Animation pour les modales */
        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-animation {
            animation: modalFadeIn 0.3s ease-out forwards;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        function openProcessModal(id) {
            const modal = document.getElementById('process-modal');
            const form = document.getElementById('process-form');
            const token = document.getElementById('process-token');

            form.action = "{{ path('app_assigned_requests_process', {'id': 'ID'}) }}".replace('ID', id);
            token.value = "{{ csrf_token('process_demande') }}";

            modal.classList.remove('hidden');
            modal.querySelector('.inline-block').classList.add('animate__animated', 'animate__fadeInDown', 'animate__faster');
        }

        function closeProcessModal() {
            const modal = document.getElementById('process-modal');
            const modalContent = modal.querySelector('.inline-block');

            // Ajouter une animation de sortie
            modalContent.classList.add('animate__fadeOutUp');

            // Attendre la fin de l'animation avant de cacher la modale
            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('animate__fadeOutUp');

                // Réinitialiser le formulaire
                document.getElementById('commentaire').value = '';
            }, 200);
        }

        function openCloseModal(id) {
            const modal = document.getElementById('close-modal');
            const form = document.getElementById('close-form');
            const token = document.getElementById('close-token');

            form.action = "{{ path('app_demande_update_status', {'id': 'ID'}) }}".replace('ID', id);
            token.value = "{{ csrf_token('update_gestionnaire') }}";

            modal.classList.remove('hidden');
            modal.querySelector('.inline-block').classList.add('animate__animated', 'animate__fadeInDown', 'animate__faster');
        }

        function closeCloseModal() {
            const modal = document.getElementById('close-modal');
            const modalContent = modal.querySelector('.inline-block');

            // Ajouter une animation de sortie
            modalContent.classList.add('animate__fadeOutUp');

            // Attendre la fin de l'animation avant de cacher la modale
            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('animate__fadeOutUp');

                // Réinitialiser le formulaire
                document.getElementById('close-commentaire').value = '';
            }, 200);
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Ajouter des classes btn-hover-effect aux boutons d'action
            document.querySelectorAll('.inline-flex.items-center').forEach(button => {
                button.classList.add('btn-hover-effect');
            });
        });
    </script>
{% endblock %}
