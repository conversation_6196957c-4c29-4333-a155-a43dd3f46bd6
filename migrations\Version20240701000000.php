<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240701000000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute les champs lienPlan, numeroOF, potentiel, delaiDemande et nomClient à la table demande';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE demande ADD lien_plan VARCHAR(255) DEFAULT NULL, ADD numero_of VARCHAR(50) DEFAULT NULL, ADD potentiel VARCHAR(20) DEFAULT NULL, ADD delai_demande DATE DEFAULT NULL, ADD nom_client VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE demande DROP lien_plan, DROP numero_of, DROP potentiel, DROP delai_demande, DROP nom_client');
    }
}
